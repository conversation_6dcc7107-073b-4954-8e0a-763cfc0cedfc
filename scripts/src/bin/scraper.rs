//! Web scraper to extract data from web pages using YAML-defined extraction rules.

// Import from the library module
use std::{
    collections::HashSet,
    io::Write,
    sync::{
        Arc,
        LazyLock,
    },
};

use clap::Parser;
use hashlink::LinkedHashMap;
use headless_chrome::Browser;
use scraper::{
    ElementRef as StaticElement,
    Html,
    Selector,
};
use scripts::scraper::*;
use slog::{
    Drain,
    debug,
    error,
    info,
};
use thiserror::Error;
use tokio::{
    task::JoinSet,
    time::Instant,
};
use yaml_rust2::{
    Yaml,
    YamlLoader,
};


/// Web scraper to extract data from web pages using YAML-defined extraction rules.
#[derive(Parser, Debug)]
#[command(version, about, long_about = None)]
struct Args {
    /// YAML file with page(s) scraping rules
    #[arg(short, long)]
    rules: String,

    /// Output file name (JSON)
    #[arg(short, long)]
    output: String,
}

// * Main

#[tokio::main]
async fn main() {
    let args = Args::parse();

    info!(LOG, "Starting job.");

    // * Load rules
    let yaml = std::fs::read_to_string(args.rules).unwrap();
    let rules = YamlLoader::load_from_str(&yaml).unwrap();
    let rule = rules.first().unwrap();

    // * Scrape
    let now = Instant::now();
    // let results = scrape_by_rules(rule).await;
    let html = Html::parse_document("<html></html>");
    let results = extract_by_rules_static(&html.root_element(), rule, false);
    info!(LOG, "Scraped in {:.0?}.", now.elapsed());

    // * Save results to a file
    let mut file = std::fs::File::create(args.output.clone()).unwrap();
    file.write_all(results.to_string().as_bytes()).unwrap();
    info!(LOG, "Saved results to {}", args.output);

    info!(LOG, "Done.");
}


// * Implementations

/// Global logger
static LOG: LazyLock<slog::Logger> = LazyLock::new(|| {
    let decorator = slog_term::PlainSyncDecorator::new(std::io::stdout());
    slog::Logger::root(slog_term::FullFormat::new(decorator).build().fuse(), slog::o!())
});
/// Headless browser driver
static DRIVER: LazyLock<Arc<Browser>> =
    LazyLock::new(|| Arc::new(Browser::default().expect("Failed to connect to browser")));
// * Models (now imported from library)

#[derive(Debug, Error)]
pub enum GetError {
    #[error("Max retries exceeded.")]
    // MaxRetries(#[from] ureq::Error),
    MaxRetries,
}

// retry function now imported from library

/// Fetches HTML content from a given URL using either static or dynamic methods.
///
/// This function can fetch web pages using two different approaches:
/// - **Dynamic**: Uses a headless browser (Chrome) to render JavaScript and get the final DOM
/// - **Static**: Uses HTTP requests to fetch the raw HTML without JavaScript execution
///
/// The function includes retry logic with up to 3 attempts for resilience against
/// transient network failures.
///
/// # Arguments
///
/// * `url` - The URL to fetch content from
/// * `dynamic` - If true, uses headless browser; if false, uses HTTP requests
///
/// # Returns
///
/// Returns the HTML content as a String on success, or GetError on failure
///
/// # Errors
///
/// Returns `GetError::MaxRetries` if all retry attempts fail due to:
/// - Network connectivity issues
/// - Invalid URLs
/// - Browser automation failures (dynamic mode)
/// - HTTP request timeouts (static mode)
///
/// # Examples
///
/// ```
/// // Static fetching (faster, no JavaScript)
/// let html = get_html_page_content_sync("https://example.com", false)?;
///
/// // Dynamic fetching (slower, executes JavaScript)
/// let html = get_html_page_content_sync("https://spa-app.com", true)?;
/// ```
fn get_html_page_content_sync(url: &str, dynamic: bool) -> Result<String, GetError> {
    const MAX_RETRIES: usize = 3;

    // * Dynamic
    if dynamic {
        match retry(MAX_RETRIES, || -> Result<String, anyhow::Error> {
            let tab = DRIVER.new_tab()?;
            tab.navigate_to(url)?;
            tab.wait_until_navigated()?;
            let html_content = tab.get_content()?;
            tab.close(false)?;

            Ok(html_content)
        }) {
            Ok(html_content) => return Ok(html_content),
            Err(err) => {
                error!(LOG, "Failed to get HTML from {url}: {err}");
                return Err(GetError::MaxRetries);
            },
        }
    }

    // * Static
    let agent: ureq::Agent = ureq::Agent::config_builder()
        .timeout_per_call(Some(std::time::Duration::from_millis(3000)))
        .build()
        .into();
    match retry(MAX_RETRIES, || agent.get(url).call()?.body_mut().read_to_string()) {
        Ok(body) => Ok(body),
        Err(err) => {
            error!(LOG, "Failed to get HTML from {url}: {err}");
            Err(GetError::MaxRetries)
        },
    }
}

/// Extracts structured data from HTML elements using YAML-defined extraction rules.
///
/// This is a comprehensive web scraping function that recursively processes HTML elements
/// to extract data according to flexible, declarative YAML rules. It supports multiple
/// data extraction strategies and can handle complex nested structures.
///
/// # Arguments
///
/// * `parent` - The HTML element to extract data from (using scraper crate's [`StaticElement`])
/// * `rule` - A YAML configuration defining the extraction strategy and data structure
/// * `debug` - Whether to enable detailed debug logging during extraction
///
/// # Returns
///
/// Returns [`ScrapedData`] which can represent:
/// - [`ScrapedData::String`] - Simple text or attribute values
/// - [`ScrapedData::Int`]/[`ScrapedData::Float`] - Parsed numeric values
/// - [`ScrapedData::Vec`] - Arrays of extracted data
/// - [`ScrapedData::Map`] - Key-value mappings of extracted data
/// - [`ScrapedData::None`] - When no data could be extracted
///
/// # Supported Rule Types
///
/// ## Value Types (controlled by `type` field):
/// - **`text`** - Extracts full text content including nested elements
/// - **`string`** - Simple text or attribute extraction (default)
/// - **`int`** - Parses extracted text as integer
/// - **`float`** - Parses extracted text as floating point number
/// - **`json`** - Parses extracted text as JSON, with optional key removal
/// - **`url`** - Extracts URLs and optionally follows them to scrape linked content
/// - **`list`** - Processes multiple elements matching a selector
/// - **`quantity`** - Counts the number of elements matching a selector
/// - **`variants`** - Tries multiple extraction strategies until one succeeds
///
/// ## Rule Structure Fields:
/// - **`selector`** - CSS selector to find target elements
/// - **`attribute`** - HTML attribute to extract (instead of text content)
/// - **`value`** - Nested rule for single-value extraction
/// - **`variants`** - Array of alternative extraction rules to try
/// - **`key`** - For dynamic maps, selector to extract the key from each element
/// - **`url`** - Static URL or URL pattern with placeholders
/// - **`follow`** - Whether to fetch and scrape content from extracted URLs
/// - **`kind`** - "dynamic" for headless browser scraping, "static" for HTTP requests
/// - **`prefix`** - URL prefix to prepend to extracted relative URLs
/// - **`remove_json_keys`** - Array of JSON keys to remove from parsed JSON
///
/// ## URL Placeholder Support:
/// URLs can contain placeholders like `{page}` which are resolved using:
/// - Arrays: `[1, 2, 3]` - Explicit list of values
/// - Ranges: `"1..10"` - Numeric range (inclusive)
///
/// # Examples
///
/// ## Simple text extraction:
/// ```yaml
/// selector: "h1"
/// type: "string"
/// ```
///
/// ## Attribute extraction:
/// ```yaml
/// selector: "img"
/// attribute: "src"
/// type: "url"
/// ```
///
/// ## List with nested structure:
/// ```yaml
/// selector: ".breed-item"
/// type: "list"
/// value:
///   - name:
///       selector: ".breed-name"
///   - origin:
///       selector: ".breed-origin"
/// ```
///
/// ## Dynamic URL scraping with placeholders:
/// ```yaml
/// selector: ""
/// type: "url"
/// url: "https://example.com/breeds?page={page}"
/// page: "1..5"
/// follow: true
/// kind: "dynamic"
/// value:
///   selector: ".breed-list"
///   type: "list"
/// ```
///
/// ## Variants (fallback strategies):
/// ```yaml
/// selector: ".breed-info"
/// type: "variants"
/// variants:
///   - strategy1:
///       selector: ".primary-info"
///   - strategy2:
///       selector: ".secondary-info"
/// ```
///
/// # Error Handling
///
/// The function is designed to be resilient:
/// - Invalid selectors return [`ScrapedData::None`]
/// - Parse failures for numbers return [`ScrapedData::None`]
/// - Network failures for URL following return [`ScrapedData::None`]
/// - Missing elements return appropriate empty collections or [`ScrapedData::None`]
///
/// # Performance Considerations
///
/// - Uses synchronous HTTP requests with 3-second timeout and 3 retry attempts
/// - Spawns blocking tasks for URL fetching to avoid blocking the runtime
/// - Supports both static HTTP scraping and dynamic headless browser rendering
/// - Recursive processing can handle deeply nested rule structures
///
/// # Thread Safety
///
/// This function uses thread-safe static instances for browser automation
/// and regex patterns. URL fetching is performed in spawned blocking tasks
/// to maintain async compatibility.
fn extract_by_rules_static(parent: &StaticElement, rule: &Yaml, debug: bool) -> ScrapedData {
    let value_type = if rule.is_array() {
        ValueType::Map
    } else if rule_contains_key(rule, Keyword::Key) {
        ValueType::DynamicMap
    } else if rule_contains_key(rule, Keyword::Url) {
        ValueType::Url
    } else if let element_type_str = rule[&*Keyword::Type].as_str().unwrap_or_default()
        && !element_type_str.is_empty()
    {
        ValueType::from_str(element_type_str)
    } else if rule_contains_key(rule, Keyword::Variants) {
        ValueType::Variants
    } else if rule_contains_key(rule, Keyword::Value) {
        ValueType::Single
    } else {
        ValueType::String
    };

    let selector = rule[&*Keyword::Selector].as_str().unwrap_or_default();
    let elements = if selector.is_empty() {
        vec![*parent]
    } else {
        let query = Selector::parse(selector).unwrap();
        parent.select(&query).collect()
    };
    let element = if selector.is_empty() {
        parent
    } else {
        let element = elements.first();
        if element.is_none() {
            if debug {
                debug!(LOG, "No element found for selector: {}", selector);
            }

            return match value_type {
                ValueType::List => ScrapedData::Vec(vec![]),
                ValueType::Map => ScrapedData::Map(LinkedHashMap::new()),
                _ => ScrapedData::None,
            };
        }
        element.unwrap()
    };
    if debug {
        debug!(
            LOG,
            "[{:?}] \"{}\" ({}): {:?}, children: {:?}\n{}\n\n",
            value_type,
            selector,
            elements.len(),
            element,
            element.child_elements().collect::<Vec<_>>(),
            element.html()
        );
    }

    match value_type {
        ValueType::Float => match get_element_text(element, rule).parse::<f32>() {
            Ok(f) => ScrapedData::Float(f),
            Err(_) => ScrapedData::None,
        },
        ValueType::Int => match get_element_text(element, rule).parse::<i32>() {
            Ok(i) => ScrapedData::Int(i),
            Err(_) => ScrapedData::None,
        },
        ValueType::DynamicMap => get_values_map_from_elements(&elements, rule, debug),
        ValueType::Json => get_json_from_element(element, rule),
        ValueType::List => get_values_list_from_elements(&elements, rule, debug),
        ValueType::Map => get_values_map_from_element(element, rule, debug),
        ValueType::Quantity =>
            if elements.is_empty() {
                ScrapedData::None
            } else {
                #[allow(clippy::cast_possible_wrap)]
                ScrapedData::Int(elements.len() as i32)
            },
        ValueType::Single | ValueType::Variants => get_nested_value_from_element(element, rule, debug),
        ValueType::String => ScrapedData::String(get_element_text(element, rule)),
        ValueType::Text => ScrapedData::String(string_to_markdown(&element.inner_html())),
        ValueType::Url => get_or_fetch_url_from_element(element, rule, debug),
    }
}

// Helper functions now imported from library

/// Extracts nested values from an element using either direct value rules or variant fallbacks.
///
/// This function handles two types of nested extraction:
/// 1. **Direct value extraction**: Uses the `value` key to apply nested rules
/// 2. **Variant fallback**: Tries multiple extraction strategies until one succeeds
///
/// For variants, it iterates through each variant rule and returns the first
/// successful extraction, allowing for robust fallback strategies when different
/// pages have different structures.
///
/// # Arguments
///
/// * `element` - The HTML element to extract data from
/// * `rule` - YAML rule containing either `value` or `variants` configuration
/// * `debug` - Whether to enable debug logging
///
/// # Returns
///
/// The extracted ScrapedData, or ScrapedData::None if no extraction succeeds
fn get_nested_value_from_element(element: &StaticElement, rule: &Yaml, debug: bool) -> ScrapedData {
    let has_variants_key = rule_contains_key(rule, Keyword::Variants);
    let has_value_key = rule_contains_key(rule, Keyword::Value);

    if has_value_key {
        return extract_by_rules_static(element, &rule[&*Keyword::Value], debug);
    }

    if has_variants_key {
        for rule_variant in rule[&*Keyword::Variants].as_vec().unwrap() {
            if !rule_variant.is_hash() {
                continue;
            }
            let result = extract_by_rules_static(
                element,
                rule_variant.as_hash().unwrap().values().next().unwrap(),
                debug,
            );
            if !matches!(result, ScrapedData::None) {
                return result;
            }
        }
    }

    ScrapedData::None
}

/// Extracts a map of values from a single HTML element using an array of extraction rules.
///
/// This function processes an array of YAML rules, where each rule is a key-value pair
/// defining what data to extract and how to extract it. It can handle both single
/// rules and multiple rules, using parallel processing for multiple rules to improve
/// performance.
///
/// # Arguments
///
/// * `element` - The HTML element to extract data from
/// * `rule` - YAML rule containing an array of extraction rules
/// * `debug` - Whether to enable debug logging
///
/// # Returns
///
/// ScrapedData::Map containing the extracted key-value pairs, sorted by key
fn get_values_map_from_element(element: &StaticElement, rule: &Yaml, debug: bool) -> ScrapedData {
    let rules = rule.as_vec().unwrap();

    let get_key_from_rule = |rule: &Yaml| {
        rule.as_hash()
            .unwrap()
            .keys()
            .next()
            .unwrap()
            .as_str()
            .unwrap()
            .to_string()
    };

    // * Single rule
    if rules.len() == 1 {
        let key = get_key_from_rule(&rules[0]);
        let value = extract_by_rules_static(element, &rules[0][key.as_str()], debug);

        return ScrapedData::Map(LinkedHashMap::from_iter(vec![(key, value)]));
    }

    // * Multiple rules
    let mut tasks = JoinSet::new();
    for r in rules {
        let key = get_key_from_rule(r);
        if debug {
            debug!(LOG, "\n\n- '{key}': {:?}", r[key.as_str()].clone());
        }
        let rule = r[key.as_str()].clone();
        let mut element_html = element.html();
        if element_html.starts_with("<tr") {
            element_html = format!("<table>{element_html}</table>");
        }
        tasks.spawn_blocking(move || {
            let html = Html::parse_fragment(&element_html);
            let element = html.root_element();
            // let debug = debug || key == "basic_details";
            if debug {
                debug!(LOG, "- '{key}': {:?}\n{}", rule, element_html);
            }
            (key, extract_by_rules_static(&element, &rule, debug))
        });
    }
    let mut results = futures::executor::block_on(tasks.join_all())
        .into_iter()
        .collect::<LinkedHashMap<_, _>>();

    // TODO: Preserve original keys order
    ScrapedData::Map(sort_map_by_key(&mut results))
}

/// Extracts a map of values from multiple HTML elements using dynamic key extraction.
///
/// This function processes multiple HTML elements where each element contributes
/// a key-value pair to the resulting map. The key is extracted using a CSS selector
/// specified in the rule's `key` field, and the value is extracted using nested
/// extraction rules.
///
/// Uses parallel processing for multiple elements to improve performance.
///
/// # Arguments
///
/// * `elements` - Vector of HTML elements to process
/// * `rule` - YAML rule containing key extraction selector and value extraction rules
/// * `debug` - Whether to enable debug logging
///
/// # Returns
///
/// ScrapedData::Map with keys extracted from elements and corresponding values
fn get_values_map_from_elements(elements: &Vec<StaticElement>, rule: &Yaml, debug: bool) -> ScrapedData {
    let has_value = rule_has_value(rule);

    // * No elements
    if elements.is_empty() {
        return ScrapedData::Map(LinkedHashMap::new());
    }

    // * Single element
    if elements.len() == 1 {
        let element = elements.first().unwrap();
        let key = get_key_from_element(element, rule);
        if key.is_empty() {
            return ScrapedData::Map(LinkedHashMap::new());
        }
        let value = if has_value {
            get_nested_value_from_element(element, rule, debug)
        } else {
            ScrapedData::None
        };

        return ScrapedData::Map(LinkedHashMap::from_iter(vec![(key, value)]));
    }

    // * Multiple elements
    let mut tasks = JoinSet::new();
    for element in elements {
        let rule = rule.clone();
        // Extract HTML content before moving into the spawned task
        let mut element_html = element.html();
        if element_html.starts_with("<tr") {
            element_html = format!("<table>{element_html}</table>");
        }
        tasks.spawn_blocking(move || {
            // Re-parse the HTML in the spawned task
            let html = Html::parse_fragment(&element_html);
            let element = html.root_element();
            let key = get_key_from_element(&element, &rule);
            let value = if !key.is_empty() && has_value {
                get_nested_value_from_element(&element, &rule, debug)
            } else {
                ScrapedData::None
            };

            (key, value)
        });
    }
    let mut results = futures::executor::block_on(tasks.join_all())
        .into_iter()
        .filter(|item| !item.0.is_empty())
        .collect::<LinkedHashMap<_, _>>();


    ScrapedData::Map(sort_map_by_key(&mut results))
}

fn get_values_list_from_elements(elements: &Vec<StaticElement>, rule: &Yaml, debug: bool) -> ScrapedData {
    if debug {
        debug!(LOG, "Scraping list of entries ({}).\n", elements.len());
    }

    // * No elements
    if elements.is_empty() {
        return ScrapedData::None;
    }

    // * Single element
    if elements.len() == 1 {
        return get_nested_value_from_element(elements.first().unwrap(), rule, debug);
    }

    // * Multiple elements
    let mut tasks = JoinSet::new();
    for element in elements {
        let rule = rule.clone();
        let element_html = element.html();
        tasks.spawn_blocking(move || {
            let html = Html::parse_fragment(&element_html);
            let element = html.root_element();

            get_nested_value_from_element(&element, &rule, debug)
        });
    }
    let results = futures::executor::block_on(tasks.join_all());

    // * If children are Map and all keys are unique, convert the Vec to a Map
    if results.iter().all(|r| matches!(r, ScrapedData::Map(_))) {
        let keys: Vec<&String> = results
            .iter()
            .flat_map(|r| {
                <ScrapedData as AsRef<LinkedHashMap<_, _>>>::as_ref(r)
                    .keys()
                    .collect::<Vec<_>>()
            })
            .collect();
        if keys.iter().collect::<HashSet<_>>().len() == keys.len() {
            let mut map = LinkedHashMap::new();
            for result in results {
                if let ScrapedData::Map(item) = result {
                    map.extend(item);
                }
            }

            return ScrapedData::Map(sort_map_by_key(&mut map));
        }
    }

    ScrapedData::Vec(results)
}

fn get_json_from_element(element: &StaticElement, rule: &Yaml) -> ScrapedData {
    let mut json: serde_json::Value =
        serde_json::from_str(&get_element_text(element, rule)).unwrap_or_default();
    // * Remove keys from the JSON that are not needed. Keys could be
    // * nested, like "breed.history.slides".
    if rule_contains_key(rule, Keyword::RemoveJsonKeys) {
        for key in rule[&*Keyword::RemoveJsonKeys].as_vec().unwrap() {
            remove_key_from_json(&mut json, key.as_str().unwrap());
        }
    }
    convert_json_to_scraped_data(&json)
}

fn get_or_fetch_url_from_element(element: &StaticElement, rule: &Yaml, debug: bool) -> ScrapedData {
    let has_url_key = rule_contains_key(rule, Keyword::Url);
    let mut url = if has_url_key {
        rule[&*Keyword::Url].as_str().unwrap()
    } else {
        let attribute = rule[&*Keyword::Attribute].as_str().unwrap_or("href");
        element.value().attr(attribute).unwrap_or_default()
    }
    .trim()
    .to_lowercase();
    if rule_contains_key(rule, Keyword::Prefix) {
        url = format!("{}{}", rule[&*Keyword::Prefix].as_str().unwrap(), url);
    }

    let has_value = rule_has_value(rule);
    let should_follow = has_url_key || rule[&*Keyword::Follow].as_bool().unwrap_or_default();
    if !has_value || !should_follow || url.matches(REGEX_URL.as_str()).count() > 0 {
        return ScrapedData::String(url);
    }

    // * Check if we have placeholders in the URL by extracting all text between `{` and `}`
    let placeholders: Vec<&str> = REGEX_PLACEHOLDER
        .captures(&url)
        .map(|caps| {
            let count = caps.len() - 1;
            (0..count)
                .map(move |i| caps.get(i + 1).unwrap().as_str())
                .collect()
        })
        .unwrap_or_default();
    if debug {
        debug!(LOG, "Placeholders: {:?}", placeholders);
    }

    // * Get placeholder values from the rule, could be a set of values or a range
    let placeholder_values = placeholders
        .into_iter()
        .map(move |placeholder| {
            let placeholder_rule = &rule[placeholder];
            if placeholder_rule.is_badvalue() {
                return (placeholder, vec![]);
            }

            // * Array of values
            if placeholder_rule.is_array() {
                return (
                    placeholder,
                    placeholder_rule
                        .as_vec()
                        .unwrap()
                        .iter()
                        .map(|v| v.as_str().unwrap().to_string())
                        .collect(),
                );
            }
            let value_str = placeholder_rule.as_str().unwrap();

            // * Range of values
            if value_str.contains("..") {
                let values = value_str.split("..").collect::<Vec<&str>>();
                let start: u16 = values[0].parse().unwrap();
                let end: u16 = values[1].parse().unwrap();

                return (placeholder, (start..=end).map(|i| i.to_string()).collect());
            }

            // * Single value
            (placeholder, vec![value_str.to_string()])
        })
        .collect::<LinkedHashMap<&str, Vec<String>>>();
    if debug {
        debug!(LOG, "Placeholder values: {:?}", &placeholder_values);
    }

    // * Get all possible URL combinations
    let mut urls = vec![url.to_string()];
    if !placeholder_values.is_empty() {
        for (placeholder, values) in placeholder_values {
            if values.is_empty() {
                return ScrapedData::None;
            }

            let mut new_urls = Vec::new();
            for value in values {
                for url in &urls {
                    new_urls.push(url.replace(&format!("{{{placeholder}}}"), &value));
                }
            }
            urls = new_urls;
        }
    }
    if debug {
        debug!(LOG, "URLs: {:?}", urls);
    }

    let is_dynamic = rule[&*Keyword::Kind].as_str().unwrap_or_default() == &*Keyword::Dynamic;

    futures::executor::block_on(scrape_from_urls(urls, rule, is_dynamic, debug))
}

async fn scrape_from_urls(urls: Vec<String>, rule: &Yaml, is_dynamic: bool, debug: bool) -> ScrapedData {
    let mut tasks = JoinSet::new();
    for url in urls {
        let rule = rule.clone();
        tasks.spawn_blocking(move || {
            info!(LOG, "Scraping {url}");
            match get_html_page_content_sync(&url, is_dynamic) {
                Ok(html_content) => {
                    let html = Html::parse_document(&html_content);
                    get_nested_value_from_element(&html.root_element(), &rule, debug)
                },
                Err(err) => {
                    error!(LOG, "Failed to get HTML from {url}: {err}");
                    ScrapedData::None
                },
            }
        });
    }
    let results = tasks.join_all().await;

    if results.is_empty() {
        ScrapedData::None
    } else if results.len() == 1 {
        results.first().unwrap().to_owned()
    } else {
        ScrapedData::Vec(results)
    }
}

// convert_json_to_scraped_data function now imported from library

// remove_key_from_json function now imported from library

// sort_map_by_key function now imported from library

// string_to_markdown function now imported from library

// Tests moved to separate files in tests/ directory
