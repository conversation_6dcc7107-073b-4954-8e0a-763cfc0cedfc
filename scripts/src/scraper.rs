//! Core scraper functionality extracted for testing

use std::{
    fmt::Display,
    ops::Deref,
    str::FromStr,
    sync::LazyLock,
};

use hashlink::LinkedHashMap;
use htmd;
use regex::Regex;
use scraper::{
    ElementRef as StaticElement,
    Selector,
};
use serde_json;
use yaml_rust2::Yaml;

// * Models

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Keyword {
    Attribute,
    Dynamic,
    Follow,
    Key,
    Kind,
    Prefix,
    Remove,
    RemoveJsonKeys,
    Selector,
    Type,
    Url,
    Value,
    Variants,
}

/// Implement transparent use as &str
impl Deref for Keyword {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        match self {
            Self::Attribute => "attribute",
            Self::Dynamic => "dynamic",
            Self::Follow => "follow",
            Self::Key => "key",
            Self::Kind => "kind",
            Self::Prefix => "prefix",
            Self::Remove => "remove",
            Self::RemoveJsonKeys => "remove_json_keys",
            Self::Selector => "selector",
            Self::Type => "type",
            Self::Url => "url",
            Self::Value => "value",
            Self::Variants => "variants",
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum ScrapedData {
    Vec(Vec<Self>),
    Map(LinkedHashMap<String, Self>),
    String(String),
    Float(f32),
    Int(i32),
    None,
}

impl Display for ScrapedData {
    fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Vec(vec) => {
                write!(fmt, "[")?;
                write!(
                    fmt,
                    "{}",
                    vec.iter()
                        .map(|item| format!("{item}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "]")
            },
            Self::Map(map) => {
                write!(fmt, "{{")?;
                write!(
                    fmt,
                    "{}",
                    map.iter()
                        .map(|(k, v)| format!("\"{k}\": {v}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "}}")
            },
            Self::String(s) => write!(
                fmt,
                "\"{}\"",
                s.replace('\\', "\\\\")
                    .replace('\"', "\\\"")
                    .replace('\r', "")
                    .replace('\n', "\\n")
            ),
            Self::Float(f) => write!(fmt, "{f:.1}"),
            Self::Int(i) => write!(fmt, "{i}"),
            Self::None => write!(fmt, "null"),
        }
    }
}

impl AsRef<Vec<Self>> for ScrapedData {
    fn as_ref(&self) -> &Vec<Self> {
        match self {
            Self::Vec(vec) => vec,
            _ => panic!("Cannot deref non-Vec"),
        }
    }
}

impl AsRef<LinkedHashMap<String, Self>> for ScrapedData {
    fn as_ref(&self) -> &LinkedHashMap<String, Self> {
        match self {
            Self::Map(map) => map,
            _ => panic!("Cannot deref non-Map"),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ValueType {
    DynamicMap,
    Float,
    Int,
    Json,
    List,
    Map,
    Single,
    Quantity,
    String,
    Text,
    Url,
    Variants,
}

impl FromStr for ValueType {
    type Err = ();

    /// Converts a string representation to a `ValueType` enum variant.
    ///
    /// This function performs case-insensitive matching and trims whitespace.
    /// If the input doesn't match any known type, it defaults to `String`.
    ///
    /// # Arguments
    ///
    /// * `s` - The string to convert to a `ValueType`
    ///
    /// # Returns
    ///
    /// The corresponding `ValueType` variant, or `String` as default
    ///
    /// # Examples
    ///
    /// ```
    /// use scripts::scraper::ValueType;
    ///
    /// assert_eq!(ValueType::from_str("LIST"), ValueType::List);
    /// assert_eq!(ValueType::from_str("  float  "), ValueType::Float);
    /// assert_eq!(ValueType::from_str("unknown"), ValueType::String);
    /// ```
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.trim().to_lowercase().as_str() {
            "dynamic_map" => Ok(Self::DynamicMap),
            "float" => Ok(Self::Float),
            "int" => Ok(Self::Int),
            "json" => Ok(Self::Json),
            "list" => Ok(Self::List),
            "quantity" => Ok(Self::Quantity),
            "text" => Ok(Self::Text),
            "url" => Ok(Self::Url),
            "variants" => Ok(Self::Variants),
            _ => Ok(Self::String),
        }
    }
}

// * Regex patterns
pub static REGEX_SPACES: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"(\s|&nbsp;)+").unwrap());
pub static REGEX_URL: LazyLock<Regex> = LazyLock::new(|| {
    Regex::new(r"^(http(s)?:\/\/)(www\\.)?[-a-zA-Z0-9@:%._\\+~#=\{\}]{2,256}\.[a-z]{2,6}\/\b([-a-zA-Z0-9@:%_\\+.~#?&\/\/=\{\}]*)$").unwrap()
});
pub static REGEX_PLACEHOLDER: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\{(.*?)\}").unwrap());
pub static REGEX_MARKDOWN_LINK: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\[([^\]]+)\]\(([^)]+)\)").unwrap());

// * Helper functions

/// Retries a fallible operation up to a maximum number of times.
///
/// This function executes the provided closure repeatedly until it succeeds
/// or the maximum number of retries is exceeded. It's useful for handling
/// transient failures in network operations or other unreliable operations.
///
/// # Arguments
///
/// * `max_retries` - Maximum number of retry attempts (not including the initial attempt)
/// * `trial` - A closure that returns a Result and can be called multiple times
///
/// # Returns
///
/// Returns the successful result if any attempt succeeds, or the last error
/// if all attempts fail.
///
/// # Errors
///
/// Returns the error from the final failed attempt if all retry attempts
/// (including the initial attempt) fail.
///
/// # Examples
///
/// ```
/// use scripts::scraper::retry;
///
/// let mut counter = 0;
/// let result = retry(3, || {
///     counter += 1;
///     if counter < 3 { Err("not ready") } else { Ok("success") }
/// });
/// assert_eq!(result, Ok("success"));
/// ```
pub fn retry<F, R, E>(max_retries: usize, mut trial: F) -> Result<R, E>
where F: FnMut() -> Result<R, E> {
    let mut retries = 0;
    loop {
        match trial() {
            Ok(result) => return Ok(result),
            Err(err) => {
                if retries >= max_retries {
                    return Err(err);
                }
                retries += 1;
            },
        }
    }
}

/// Checks if a YAML rule contains a specific key.
///
/// This function tests whether a YAML rule object contains a given keyword
/// by checking if the value is not a "badvalue" (YAML's representation of
/// missing or invalid keys).
///
/// # Arguments
///
/// * `rule` - The YAML rule to check
/// * `key` - The keyword to look for
///
/// # Returns
///
/// `true` if the key exists and has a valid value, `false` otherwise
#[must_use]
#[allow(clippy::needless_pass_by_value)]
pub fn rule_contains_key(rule: &Yaml, key: Keyword) -> bool { !rule[&*key].is_badvalue() }

/// Checks if a YAML rule has a value extraction configuration.
///
/// A rule is considered to have a value if it contains either a `value` key
/// (for single value extraction) or a `variants` key (for multiple extraction
/// strategies).
///
/// # Arguments
///
/// * `rule` - The YAML rule to check
///
/// # Returns
///
/// `true` if the rule can extract values, `false` otherwise
#[must_use]
pub fn rule_has_value(rule: &Yaml) -> bool {
    let has_value_key = rule_contains_key(rule, Keyword::Value);
    let has_variants_key = rule_contains_key(rule, Keyword::Variants);
    has_value_key || has_variants_key
}

/// Extracts text content from an HTML element based on rule configuration.
///
/// This function can extract either the text content of an element or the value
/// of a specific HTML attribute, depending on the rule configuration.
///
/// # Arguments
///
/// * `element` - The HTML element to extract text from
/// * `rule` - YAML rule that may contain an `attribute` key specifying which attribute to extract
///
/// # Returns
///
/// The extracted and trimmed text content or attribute value
///
/// # Panics
///
/// Panics if the rule contains an `attribute` key but the attribute name cannot be
/// converted to a string (i.e., if `rule[&*Keyword::Attribute].as_str()` returns `None`).
///
/// # Examples
///
/// For text extraction:
/// ```html
/// <div>Hello World</div>
/// ```
///
/// For attribute extraction:
/// ```html
/// <img src="image.jpg" alt="description">
/// ```
/// With rule containing `attribute: "src"`, returns "image.jpg"
#[must_use]
pub fn get_element_text(element: &StaticElement, rule: &Yaml) -> String {
    if rule_contains_key(rule, Keyword::Attribute) {
        element
            .value()
            .attr(rule[&*Keyword::Attribute].as_str().unwrap())
            .unwrap_or_default()
            .trim()
            .to_string()
    } else {
        element.text().next().unwrap_or_default().trim().to_string()
    }
}

/// Extracts a normalized key from an HTML element using a CSS selector.
///
/// This function finds the first element matching the selector specified in the
/// rule's `key` field, extracts its text content, and normalizes it for use as
/// a map key by converting to lowercase and replacing spaces with underscores.
///
/// # Arguments
///
/// * `element` - The parent HTML element to search within
/// * `rule` - YAML rule containing a `key` field with a CSS selector
///
/// # Returns
///
/// A normalized string suitable for use as a map key, or empty string if no element found
///
/// # Panics
///
/// Panics if:
/// - The rule's `key` field cannot be converted to a string
/// - The CSS selector in the `key` field is invalid and cannot be parsed
///
/// # Examples
///
/// For HTML:
/// ```html
/// <div><h3>Product Name</h3><p>content</p></div>
/// ```
/// With rule `key: "h3"`, returns "`product_name`"
pub fn get_key_from_element(element: &StaticElement, rule: &Yaml) -> String {
    let selector = Selector::parse(rule[&*Keyword::Key].as_str().unwrap()).unwrap();
    let key_elements = element.select(&selector).collect::<Vec<_>>();
    if key_elements.is_empty() {
        return String::new();
    }

    let key = key_elements
        .first()
        .unwrap()
        .text()
        .next()
        .unwrap()
        .trim()
        .to_lowercase();

    REGEX_SPACES.replace_all(&key, "_").to_string()
}

/// Recursively removes a key from a JSON value structure.
///
/// This function supports both simple keys and nested keys using dot notation.
/// For nested keys like "outer.inner", it will traverse the JSON structure
/// and remove the specified nested key. When applied to arrays, it recursively
/// removes the key from all objects within the array.
///
/// # Arguments
///
/// * `json` - Mutable reference to the JSON value to modify
/// * `key` - The key to remove, supporting dot notation for nested keys
///
/// # Panics
///
/// Panics if the key contains a dot but `split_once('.')` fails, which should
/// never happen since we check `key.contains('.')` first.
///
/// # Examples
///
/// Simple key removal:
/// ```json
/// {"keep": "this", "remove": "that"} -> {"keep": "this"}
/// ```
///
/// Nested key removal with "outer.inner":
/// ```json
/// {"outer": {"inner": "value", "keep": "this"}} -> {"outer": {"keep": "this"}}
/// ```
///
/// Array handling:
/// ```json
/// [{"key": "value1"}, {"key": "value2"}] -> [{}, {}]
/// ```
pub fn remove_key_from_json(json: &mut serde_json::Value, key: &str) {
    if key.contains('.') {
        let (first, rest) = key.split_once('.').unwrap();
        if let Some(json) = json.get_mut(first) {
            remove_key_from_json(json, rest);
        }
        return;
    }

    if json.is_object() {
        json.as_object_mut().unwrap().remove(key);
    } else if json.is_array() {
        for json in json.as_array_mut().unwrap() {
            remove_key_from_json(json, key);
        }
    }
}

/// Sorts a `LinkedHashMap` by its keys and returns a new sorted map.
///
/// This function extracts all keys from the input map, sorts them alphabetically,
/// and creates a new `LinkedHashMap` with entries in the sorted key order. The
/// original map is consumed in the process.
///
/// # Arguments
///
/// * `map` - Mutable reference to the map to sort (will be emptied)
///
/// # Returns
///
/// A new `LinkedHashMap` with the same entries but sorted by key
///
/// # Panics
///
/// Panics if a key that was present during iteration is not found when trying
/// to remove it from the map, which should never happen in normal operation.
///
/// # Examples
///
/// ```
/// use hashlink::LinkedHashMap;
/// use scripts::scraper::{
///     ScrapedData,
///     sort_map_by_key,
/// };
///
/// let mut map = LinkedHashMap::new();
/// map.insert("zebra".to_string(), ScrapedData::String("z".to_string()));
/// map.insert("alpha".to_string(), ScrapedData::String("a".to_string()));
///
/// let sorted = sort_map_by_key(&mut map);
/// // Keys are now in order: ["alpha", "zebra"]
/// ```
pub fn sort_map_by_key<T>(map: &mut LinkedHashMap<String, T>) -> LinkedHashMap<String, T> {
    let mut keys = map.keys().cloned().collect::<Vec<_>>();
    keys.sort();

    keys.into_iter()
        .map(|k| (k.clone(), map.remove(&k).unwrap()))
        .collect()
}

/// Converts a `serde_json::Value` to `ScrapedData` recursively.
///
/// This function transforms JSON values into the internal `ScrapedData` representation
/// used throughout the scraper. It handles all JSON value types and recursively
/// processes nested structures. String values are converted to markdown format.
///
/// # Arguments
///
/// * `json` - The JSON value to convert
///
/// # Returns
///
/// The corresponding `ScrapedData` representation
///
/// # Panics
///
/// Panics if:
/// - A JSON number cannot be converted to i64 or f64 (should never happen with valid JSON)
/// - The `string_to_markdown` function panics on string conversion
/// - The `sort_map_by_key` function panics during map sorting
///
/// # Type Conversions
///
/// - `null` → `ScrapedData::None`
/// - `boolean` → `ScrapedData::String` (stringified)
/// - `integer` → `ScrapedData::Int` (as i32)
/// - `float` → `ScrapedData::Float` (as f32)
/// - `string` → `ScrapedData::String` (converted to markdown)
/// - `array` → `ScrapedData::Vec` (recursively converted)
/// - `object` → `ScrapedData::Map` (recursively converted and sorted by key)
///
/// # Examples
///
/// ```
/// use serde_json::json;
/// use scripts::scraper::convert_json_to_scraped_data;
///
/// let json = json!({"name": "test", "count": 42});
/// let scraped = convert_json_to_scraped_data(&json);
/// // Returns ScrapedData::Map with sorted keys
/// ```
#[must_use]
pub fn convert_json_to_scraped_data(json: &serde_json::Value) -> ScrapedData {
    match json {
        serde_json::Value::Null => ScrapedData::None,
        serde_json::Value::Bool(b) => ScrapedData::String(b.to_string()),
        serde_json::Value::Number(n) =>
            if n.is_i64() {
                ScrapedData::Int(n.as_i64().unwrap() as i32)
            } else if n.is_f64() {
                ScrapedData::Float(n.as_f64().unwrap() as f32)
            } else {
                ScrapedData::None
            },
        serde_json::Value::String(s) => ScrapedData::String(string_to_markdown(s)),
        serde_json::Value::Array(a) => {
            let mut results = Vec::new();
            for json in a {
                results.push(convert_json_to_scraped_data(json));
            }
            ScrapedData::Vec(results)
        },
        serde_json::Value::Object(o) => {
            let mut results = LinkedHashMap::new();
            for (k, v) in o {
                results.insert(k.clone(), convert_json_to_scraped_data(v));
            }
            ScrapedData::Map(sort_map_by_key(&mut results))
        },
    }
}

/// Converts HTML text to clean markdown format.
///
/// This function takes HTML content and converts it to markdown, then performs
/// additional cleanup operations:
/// - Normalizes whitespace (multiple spaces/nbsp become single spaces)
/// - Removes markdown links, keeping only the link text
/// - Removes carriage returns
/// - Filters out empty lines
/// - Joins lines with newlines
///
/// # Arguments
///
/// * `text` - The HTML text to convert to markdown
///
/// # Returns
///
/// Clean markdown text with normalized whitespace and simplified formatting
///
/// # Panics
///
/// Panics if the HTML-to-markdown conversion fails, which can happen with
/// malformed HTML input that cannot be processed by the htmd library.
///
/// # Examples
///
/// ```
/// use scripts::scraper::string_to_markdown;
///
/// let html = "<p>Hello <strong>World</strong></p>";
/// let markdown = string_to_markdown(html);
/// // Returns cleaned markdown text
/// ```
#[must_use]
pub fn string_to_markdown(text: &str) -> String {
    htmd::convert(text).unwrap()
        .split('\n')
        // * Remove double spaces and markdown links keeping only the placeholder name
        .map(|s|
            REGEX_SPACES.replace_all(
                REGEX_MARKDOWN_LINK.replace_all(s, "$1").trim(),
                " ",
            ).replace('\r', ""),
        )
        .filter(|s| !s.is_empty())
        .collect::<Vec<_>>()
        .join("\n")
}

/// Extracts JSON data from an HTML element and processes it according to YAML rules.
///
/// This function parses JSON content from an element's text and optionally removes
/// specified keys from the resulting JSON structure.
///
/// # Arguments
///
/// * `element` - The HTML element containing JSON text
/// * `rule` - YAML rule that may contain `remove` key with keys to remove from JSON
///
/// # Returns
///
/// Returns `ScrapedData` containing the parsed and processed JSON data.
///
/// # Panics
///
/// Panics if:
/// - The rule contains a `remove` key but its value cannot be converted to a vector
/// - Any key in the `remove` array cannot be converted to a string
///
/// # Examples
///
/// ```html
/// <script type="application/json">{"name": "Product", "price": 29.99, "internal_id": 123}</script>
/// ```
///
/// With rule:
/// ```yaml
/// remove: ["internal_id"]
/// ```
///
/// Returns JSON without the `internal_id` field.
#[must_use]
pub fn get_json_from_element(element: &StaticElement, rule: &Yaml) -> ScrapedData {
    let mut json: serde_json::Value =
        serde_json::from_str(&get_element_text(element, rule)).unwrap_or_default();

    // Remove keys from the JSON that are not needed
    if rule_contains_key(rule, Keyword::Remove) {
        for key in rule[&*Keyword::Remove].as_vec().unwrap() {
            remove_key_from_json(&mut json, key.as_str().unwrap());
        }
    }

    convert_json_to_scraped_data(&json)
}

/// Extracts and processes URL from an HTML element according to YAML rules.
///
/// This function handles URL extraction, prefix addition, and placeholder processing
/// without performing actual network requests. It's the testable part of URL processing.
///
/// # Arguments
///
/// * `element` - The HTML element to extract URL from
/// * `rule` - YAML rule defining URL extraction and processing
///
/// # Returns
///
/// Returns a tuple of (`processed_url`, `should_follow`, `placeholder_values`) where:
/// - `processed_url` is the URL after prefix and basic processing
/// - `should_follow` indicates if the URL should be fetched
/// - `placeholder_values` contains resolved placeholder values for URL templates
///
/// # Panics
///
/// Panics if:
/// - The rule contains a `url` key but its value cannot be converted to a string
/// - The rule contains an `attribute` key but its value cannot be converted to a string
/// - The rule contains a `prefix` key but its value cannot be converted to a string
///
/// # Examples
///
/// ```html
/// <a href="/product/123">Product Link</a>
/// ```
///
/// With rule:
/// ```yaml
/// attribute: "href"
/// prefix: "https://example.com"
/// follow: true
/// ```
///
/// Returns: ("<https://example.com/product/123>", true, {})
#[must_use]
pub fn process_url_from_element(
    element: &StaticElement,
    rule: &Yaml,
) -> (String, bool, LinkedHashMap<String, Vec<String>>) {
    let has_url_key = rule_contains_key(rule, Keyword::Url);
    let mut url = if has_url_key {
        rule[&*Keyword::Url].as_str().unwrap()
    } else {
        let attribute = rule[&*Keyword::Attribute].as_str().unwrap_or("href");
        element.value().attr(attribute).unwrap_or_default()
    }
    .trim()
    .to_lowercase();

    // Add prefix if specified
    if rule_contains_key(rule, Keyword::Prefix) {
        url = format!("{}{}", rule[&*Keyword::Prefix].as_str().unwrap(), url);
    }

    let has_value = rule_has_value(rule);
    let should_follow = has_url_key || rule[&*Keyword::Follow].as_bool().unwrap_or_default();

    // If we shouldn't follow or don't have value rules, return early
    if !has_value || !should_follow {
        return (url, false, LinkedHashMap::new());
    }

    // Process placeholders
    let placeholder_values = extract_placeholder_values(&url, rule);

    (url, should_follow, placeholder_values)
}

/// Extracts placeholder values from a URL template according to YAML rules.
///
/// This function processes URL templates with placeholders like `{id}` and resolves
/// their values from the YAML rule configuration.
///
/// # Arguments
///
/// * `url` - URL template with placeholders in `{name}` format
/// * `rule` - YAML rule containing placeholder value definitions
///
/// # Returns
///
/// Returns a `LinkedHashMap` where keys are placeholder names and values are
/// vectors of possible values for each placeholder.
///
/// # Panics
///
/// Panics if:
/// - The regex pattern for extracting placeholders is invalid (should never happen with the hardcoded
///   pattern)
/// - Range parsing fails when a placeholder value contains ".." but cannot be parsed as integers
/// - Any placeholder value in an array cannot be converted to a string
///
/// # Examples
///
/// URL: `"https://api.com/items/{id}/details"`
/// Rule with: `id: ["1", "2", "3"]`
/// Returns: `{"id": ["1", "2", "3"]}`
#[must_use]
pub fn extract_placeholder_values(url: &str, rule: &Yaml) -> LinkedHashMap<String, Vec<String>> {
    // Extract placeholders from URL (text between { and })
    let placeholder_regex = regex::Regex::new(r"\{([^}]+)\}").unwrap();
    let placeholders: Vec<String> = placeholder_regex
        .captures_iter(url)
        .map(|cap| cap[1].to_string())
        .collect();

    let mut placeholder_values = LinkedHashMap::new();

    for placeholder in placeholders {
        let placeholder_rule = &rule[placeholder.as_str()];
        if placeholder_rule.is_badvalue() {
            placeholder_values.insert(placeholder, vec![]);
            continue;
        }

        let values = if placeholder_rule.is_array() {
            // Array of values
            placeholder_rule
                .as_vec()
                .unwrap()
                .iter()
                .map(|v| v.as_str().unwrap_or_default().to_string())
                .collect()
        } else {
            let value_str = placeholder_rule.as_str().unwrap_or_default();

            if value_str.contains("..") {
                // Range of values (e.g., "1..10")
                let parts: Vec<&str> = value_str.split("..").collect();
                if parts.len() == 2 {
                    if let (Ok(start), Ok(end)) = (parts[0].parse::<u16>(), parts[1].parse::<u16>()) {
                        (start..=end).map(|i| i.to_string()).collect()
                    } else {
                        vec![value_str.to_string()]
                    }
                } else {
                    vec![value_str.to_string()]
                }
            } else {
                // Single value
                vec![value_str.to_string()]
            }
        };

        placeholder_values.insert(placeholder, values);
    }

    placeholder_values
}

/// Generates all possible URL combinations from a template and placeholder values.
///
/// This function takes a URL template with placeholders and generates all possible
/// concrete URLs by substituting placeholder values.
///
/// # Arguments
///
/// * `template_url` - URL template with placeholders in `{name}` format
/// * `placeholder_values` - Map of placeholder names to their possible values
///
/// # Returns
///
/// Returns a vector of all possible URL combinations.
///
/// # Examples
///
/// Template: `"https://api.com/items/{id}/type/{type}"`
/// Placeholders: `{"id": ["1", "2"], "type": ["A", "B"]}`
/// Returns: `["https://api.com/items/1/type/A", "https://api.com/items/1/type/B",
///           "https://api.com/items/2/type/A", "https://api.com/items/2/type/B"]`
#[must_use]
pub fn generate_url_combinations(
    template_url: &str,
    placeholder_values: &LinkedHashMap<String, Vec<String>>,
) -> Vec<String> {
    if placeholder_values.is_empty() {
        return vec![template_url.to_string()];
    }

    let mut urls = vec![template_url.to_string()];

    for (placeholder, values) in placeholder_values {
        if values.is_empty() {
            return vec![]; // If any placeholder has no values, return empty
        }

        let mut new_urls = Vec::new();
        for value in values {
            for url in &urls {
                new_urls.push(url.replace(&format!("{{{placeholder}}}"), value));
            }
        }
        urls = new_urls;
    }

    urls
}

/// Processes nested value extraction rules to determine extraction strategy.
///
/// This function analyzes YAML rules to determine how to extract nested values,
/// handling both `value` and `variants` rule types. This is the testable logic
/// from `get_nested_value_from_element` without the actual extraction.
///
/// # Arguments
///
/// * `rule` - YAML rule that may contain `value` or `variants` keys
///
/// # Returns
///
/// Returns a tuple of (`has_value_key`, `has_variants_key`, `variant_count`) where:
/// - `has_value_key` indicates if the rule has a `value` key for direct extraction
/// - `has_variants_key` indicates if the rule has a `variants` key for fallback extraction
/// - `variant_count` is the number of variant rules available
///
/// # Examples
///
/// Rule with value:
/// ```yaml
/// value:
///   selector: "span"
///   type: "string"
/// ```
/// Returns: (true, false, 0)
///
/// Rule with variants:
/// ```yaml
/// variants:
///   - in_stock:
///       selector: ".in-stock"
///   - out_of_stock:
///       selector: ".out-of-stock"
/// ```
/// Returns: (false, true, 2)
#[must_use]
pub fn analyze_nested_value_rule(rule: &Yaml) -> (bool, bool, usize) {
    let has_value_key = rule_contains_key(rule, Keyword::Value);
    let has_variants_key = rule_contains_key(rule, Keyword::Variants);

    let variant_count = if has_variants_key {
        rule[&*Keyword::Variants]
            .as_vec()
            .map_or(0, |variants| variants.iter().filter(|v| v.is_hash()).count())
    } else {
        0
    };

    (has_value_key, has_variants_key, variant_count)
}

/// Extracts rule keys from an array of YAML extraction rules.
///
/// This function processes an array of YAML rules where each rule is a key-value pair,
/// extracting the keys that will be used in the resulting data map. This is the
/// testable logic from `get_values_map_from_element` without the actual extraction.
///
/// # Arguments
///
/// * `rule` - YAML rule containing an array of extraction rules
///
/// # Returns
///
/// Returns a vector of extracted keys in the order they appear in the rules.
///
/// # Examples
///
/// Rule array:
/// ```yaml
/// - title:
///     selector: "h1"
///     type: "string"
/// - price:
///     selector: ".price"
///     type: "float"
/// ```
/// Returns: `["title", "price"]`
#[must_use]
pub fn extract_map_rule_keys(rule: &Yaml) -> Vec<String> {
    if !rule.is_array() {
        return vec![];
    }

    rule.as_vec()
        .unwrap()
        .iter()
        .filter_map(|r| {
            if r.is_hash() {
                r.as_hash()
                    .unwrap()
                    .keys()
                    .next()
                    .and_then(|k| k.as_str())
                    .map(std::string::ToString::to_string)
            } else {
                None
            }
        })
        .collect()
}

/// Analyzes dynamic map extraction rules for key-value pair processing.
///
/// This function examines YAML rules for dynamic map extraction where keys are
/// extracted from HTML elements using CSS selectors. This is the testable logic
/// from `get_values_map_from_elements` without the actual extraction.
///
/// # Arguments
///
/// * `rule` - YAML rule that may contain `key` selector and `value` extraction rules
///
/// # Returns
///
/// Returns a tuple of (`has_key_selector`, `has_value_rules`, `key_selector`) where:
/// - `has_key_selector` indicates if the rule has a key extraction selector
/// - `has_value_rules` indicates if the rule has value extraction rules
/// - `key_selector` is the CSS selector for extracting keys (empty if not present)
///
/// # Examples
///
/// Rule with key and value:
/// ```yaml
/// key:
///   selector: ".name"
/// value:
///   selector: ".content"
///   type: "string"
/// ```
/// Returns: (true, true, ".name")
#[must_use]
pub fn analyze_dynamic_map_rule(rule: &Yaml) -> (bool, bool, String) {
    let has_key_selector = rule_contains_key(rule, Keyword::Key);
    let has_value_rules = rule_has_value(rule);

    let key_selector = if has_key_selector {
        rule[&*Keyword::Key][&*Keyword::Selector]
            .as_str()
            .unwrap_or_default()
            .to_string()
    } else {
        String::new()
    };

    (has_key_selector, has_value_rules, key_selector)
}

/// Analyzes list extraction rules and determines processing strategy.
///
/// This function examines YAML rules for list extraction to determine how
/// the results should be processed and combined. This is the testable logic
/// from `get_values_list_from_elements` without the actual extraction.
///
/// # Arguments
///
/// * `rule` - YAML rule for list extraction
/// * `element_count` - Number of elements that would be processed
///
/// # Returns
///
/// Returns a tuple of (`should_return_single`, `should_merge_maps`, `processing_strategy`) where:
/// - `should_return_single` indicates if single element should be returned directly
/// - `should_merge_maps` indicates if map results should be merged into one map
/// - `processing_strategy` describes how multiple results should be handled
///
/// # Examples
///
/// For 1 element: Returns (true, false, "single")
/// For multiple elements with map results: Returns (false, true, "`merge_maps`")
/// For multiple elements with mixed results: Returns (false, false, "list")
#[must_use]
pub const fn analyze_list_processing_strategy(
    _rule: &Yaml,
    element_count: usize,
) -> (bool, bool, &'static str) {
    match element_count {
        0 => (false, false, "empty"),
        1 => (true, false, "single"),
        _ => (false, false, "multiple"), // Simplified - actual logic depends on result types
    }
}
