//! Core scraper functionality extracted for testing

use std::{
    fmt::Display,
    ops::Deref,
    sync::LazyLock,
};

use hashlink::LinkedHashMap;
use htmd;
use regex::Regex;
use scraper::{
    ElementRef as StaticElement,
    Selector,
};
use serde_json;
use yaml_rust2::Yaml;

// * Models

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Keyword {
    Attribute,
    Dynamic,
    Follow,
    Key,
    Kind,
    Prefix,
    RemoveJsonKeys,
    Selector,
    Type,
    Url,
    Value,
    Variants,
}

/// Implement transparent use as &str
impl Deref for Keyword {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        match self {
            Self::Attribute => "attribute",
            Self::Dynamic => "dynamic",
            Self::Follow => "follow",
            Self::Key => "key",
            Self::Kind => "kind",
            Self::Prefix => "prefix",
            Self::RemoveJsonKeys => "remove_json_keys",
            Self::Selector => "selector",
            Self::Type => "type",
            Self::Url => "url",
            Self::Value => "value",
            Self::Variants => "variants",
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum ScrapedData {
    Vec(Vec<Self>),
    Map(LinkedHashMap<String, Self>),
    String(String),
    Float(f32),
    Int(i32),
    None,
}

impl Display for ScrapedData {
    fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Vec(vec) => {
                write!(fmt, "[")?;
                write!(
                    fmt,
                    "{}",
                    vec.iter()
                        .map(|item| format!("{item}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "]")
            },
            Self::Map(map) => {
                write!(fmt, "{{")?;
                write!(
                    fmt,
                    "{}",
                    map.iter()
                        .map(|(k, v)| format!("\"{k}\": {v}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "}}")
            },
            Self::String(s) => write!(
                fmt,
                "\"{}\"",
                s.replace('\\', "\\\\")
                    .replace('\"', "\\\"")
                    .replace('\r', "")
                    .replace('\n', "\\n")
            ),
            Self::Float(f) => write!(fmt, "{f:.1}"),
            Self::Int(i) => write!(fmt, "{i}"),
            Self::None => write!(fmt, "null"),
        }
    }
}

impl AsRef<Vec<Self>> for ScrapedData {
    fn as_ref(&self) -> &Vec<Self> {
        match self {
            Self::Vec(vec) => vec,
            _ => panic!("Cannot deref non-Vec"),
        }
    }
}

impl AsRef<LinkedHashMap<String, Self>> for ScrapedData {
    fn as_ref(&self) -> &LinkedHashMap<String, Self> {
        match self {
            Self::Map(map) => map,
            _ => panic!("Cannot deref non-Map"),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ValueType {
    DynamicMap,
    Float,
    Int,
    Json,
    List,
    Map,
    Single,
    Quantity,
    String,
    Text,
    Url,
    Variants,
}

impl ValueType {
    /// Converts a string representation to a `ValueType` enum variant.
    ///
    /// This function performs case-insensitive matching and trims whitespace.
    /// If the input doesn't match any known type, it defaults to `String`.
    ///
    /// # Arguments
    ///
    /// * `s` - The string to convert to a `ValueType`
    ///
    /// # Returns
    ///
    /// The corresponding `ValueType` variant, or `String` as default
    ///
    /// # Examples
    ///
    /// ```
    /// use scripts::scraper::ValueType;
    ///
    /// assert_eq!(ValueType::from_str("LIST"), ValueType::List);
    /// assert_eq!(ValueType::from_str("  float  "), ValueType::Float);
    /// assert_eq!(ValueType::from_str("unknown"), ValueType::String);
    /// ```
    #[must_use]
    pub fn from_str(s: &str) -> Self {
        match s.trim().to_lowercase().as_str() {
            "dynamic_map" => Self::DynamicMap,
            "float" => Self::Float,
            "int" => Self::Int,
            "json" => Self::Json,
            "list" => Self::List,
            "quantity" => Self::Quantity,
            "text" => Self::Text,
            "url" => Self::Url,
            "variants" => Self::Variants,
            _ => Self::String,
        }
    }
}

// * Regex patterns
pub static REGEX_SPACES: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"(\s|&nbsp;)+").unwrap());
pub static REGEX_URL: LazyLock<Regex> = LazyLock::new(|| {
    Regex::new(r"^(http(s)?:\/\/)(www\\.)?[-a-zA-Z0-9@:%._\\+~#=\{\}]{2,256}\.[a-z]{2,6}\/\b([-a-zA-Z0-9@:%_\\+.~#?&\/\/=\{\}]*)$").unwrap()
});
pub static REGEX_PLACEHOLDER: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\{(.*?)\}").unwrap());
pub static REGEX_MARKDOWN_LINK: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\[([^\]]+)\]\(([^)]+)\)").unwrap());

// * Helper functions

/// Retries a fallible operation up to a maximum number of times.
///
/// This function executes the provided closure repeatedly until it succeeds
/// or the maximum number of retries is exceeded. It's useful for handling
/// transient failures in network operations or other unreliable operations.
///
/// # Arguments
///
/// * `max_retries` - Maximum number of retry attempts (not including the initial attempt)
/// * `trial` - A closure that returns a Result and can be called multiple times
///
/// # Returns
///
/// Returns the successful result if any attempt succeeds, or the last error
/// if all attempts fail.
///
/// # Examples
///
/// ```
/// use scripts::scraper::retry;
///
/// let mut counter = 0;
/// let result = retry(3, || {
///     counter += 1;
///     if counter < 3 { Err("not ready") } else { Ok("success") }
/// });
/// assert_eq!(result, Ok("success"));
/// ```
pub fn retry<F, R, E>(max_retries: usize, mut trial: F) -> Result<R, E>
where F: FnMut() -> Result<R, E> {
    let mut retries = 0;
    loop {
        match trial() {
            Ok(result) => return Ok(result),
            Err(err) => {
                if retries >= max_retries {
                    return Err(err);
                }
                retries += 1;
            },
        }
    }
}

/// Checks if a YAML rule contains a specific key.
///
/// This function tests whether a YAML rule object contains a given keyword
/// by checking if the value is not a "badvalue" (YAML's representation of
/// missing or invalid keys).
///
/// # Arguments
///
/// * `rule` - The YAML rule to check
/// * `key` - The keyword to look for
///
/// # Returns
///
/// `true` if the key exists and has a valid value, `false` otherwise
#[must_use]
pub fn rule_contains_key(rule: &Yaml, key: Keyword) -> bool { !rule[&*key].is_badvalue() }

/// Checks if a YAML rule has a value extraction configuration.
///
/// A rule is considered to have a value if it contains either a `value` key
/// (for single value extraction) or a `variants` key (for multiple extraction
/// strategies).
///
/// # Arguments
///
/// * `rule` - The YAML rule to check
///
/// # Returns
///
/// `true` if the rule can extract values, `false` otherwise
#[must_use]
pub fn rule_has_value(rule: &Yaml) -> bool {
    let has_value_key = rule_contains_key(rule, Keyword::Value);
    let has_variants_key = rule_contains_key(rule, Keyword::Variants);
    has_value_key || has_variants_key
}

/// Extracts text content from an HTML element based on rule configuration.
///
/// This function can extract either the text content of an element or the value
/// of a specific HTML attribute, depending on the rule configuration.
///
/// # Arguments
///
/// * `element` - The HTML element to extract text from
/// * `rule` - YAML rule that may contain an `attribute` key specifying which attribute to extract
///
/// # Returns
///
/// The extracted and trimmed text content or attribute value
///
/// # Examples
///
/// For text extraction:
/// ```html
/// <div>Hello World</div>
/// ```
///
/// For attribute extraction:
/// ```html
/// <img src="image.jpg" alt="description">
/// ```
/// With rule containing `attribute: "src"`, returns "image.jpg"
#[must_use]
pub fn get_element_text(element: &StaticElement, rule: &Yaml) -> String {
    if rule_contains_key(rule, Keyword::Attribute) {
        element
            .value()
            .attr(rule[&*Keyword::Attribute].as_str().unwrap())
            .unwrap_or_default()
            .trim()
            .to_string()
    } else {
        element.text().next().unwrap_or_default().trim().to_string()
    }
}

/// Extracts a normalized key from an HTML element using a CSS selector.
///
/// This function finds the first element matching the selector specified in the
/// rule's `key` field, extracts its text content, and normalizes it for use as
/// a map key by converting to lowercase and replacing spaces with underscores.
///
/// # Arguments
///
/// * `element` - The parent HTML element to search within
/// * `rule` - YAML rule containing a `key` field with a CSS selector
///
/// # Returns
///
/// A normalized string suitable for use as a map key, or empty string if no element found
///
/// # Examples
///
/// For HTML:
/// ```html
/// <div><h3>Product Name</h3><p>content</p></div>
/// ```
/// With rule `key: "h3"`, returns "`product_name`"
pub fn get_key_from_element(element: &StaticElement, rule: &Yaml) -> String {
    let selector = Selector::parse(rule[&*Keyword::Key].as_str().unwrap()).unwrap();
    let key_elements = element.select(&selector).collect::<Vec<_>>();
    if key_elements.is_empty() {
        return String::new();
    }

    let key = key_elements
        .first()
        .unwrap()
        .text()
        .next()
        .unwrap()
        .trim()
        .to_lowercase();

    REGEX_SPACES.replace_all(&key, "_").to_string()
}

/// Recursively removes a key from a JSON value structure.
///
/// This function supports both simple keys and nested keys using dot notation.
/// For nested keys like "outer.inner", it will traverse the JSON structure
/// and remove the specified nested key. When applied to arrays, it recursively
/// removes the key from all objects within the array.
///
/// # Arguments
///
/// * `json` - Mutable reference to the JSON value to modify
/// * `key` - The key to remove, supporting dot notation for nested keys
///
/// # Examples
///
/// Simple key removal:
/// ```json
/// {"keep": "this", "remove": "that"} -> {"keep": "this"}
/// ```
///
/// Nested key removal with "outer.inner":
/// ```json
/// {"outer": {"inner": "value", "keep": "this"}} -> {"outer": {"keep": "this"}}
/// ```
///
/// Array handling:
/// ```json
/// [{"key": "value1"}, {"key": "value2"}] -> [{}, {}]
/// ```
pub fn remove_key_from_json(json: &mut serde_json::Value, key: &str) {
    if key.contains('.') {
        let (first, rest) = key.split_once('.').unwrap();
        if let Some(json) = json.get_mut(first) {
            remove_key_from_json(json, rest);
        }
        return;
    }

    if json.is_object() {
        json.as_object_mut().unwrap().remove(key);
    } else if json.is_array() {
        for json in json.as_array_mut().unwrap() {
            remove_key_from_json(json, key);
        }
    }
}

/// Sorts a `LinkedHashMap` by its keys and returns a new sorted map.
///
/// This function extracts all keys from the input map, sorts them alphabetically,
/// and creates a new `LinkedHashMap` with entries in the sorted key order. The
/// original map is consumed in the process.
///
/// # Arguments
///
/// * `map` - Mutable reference to the map to sort (will be emptied)
///
/// # Returns
///
/// A new `LinkedHashMap` with the same entries but sorted by key
///
/// # Examples
///
/// ```
/// use hashlink::LinkedHashMap;
/// use scripts::scraper::{
///     ScrapedData,
///     sort_map_by_key,
/// };
///
/// let mut map = LinkedHashMap::new();
/// map.insert("zebra".to_string(), ScrapedData::String("z".to_string()));
/// map.insert("alpha".to_string(), ScrapedData::String("a".to_string()));
///
/// let sorted = sort_map_by_key(&mut map);
/// // Keys are now in order: ["alpha", "zebra"]
/// ```
pub fn sort_map_by_key<T>(map: &mut LinkedHashMap<String, T>) -> LinkedHashMap<String, T> {
    let mut keys = map.keys().cloned().collect::<Vec<_>>();
    keys.sort();

    keys.into_iter()
        .map(|k| (k.clone(), map.remove(&k).unwrap()))
        .collect()
}

/// Converts a `serde_json::Value` to `ScrapedData` recursively.
///
/// This function transforms JSON values into the internal `ScrapedData` representation
/// used throughout the scraper. It handles all JSON value types and recursively
/// processes nested structures. String values are converted to markdown format.
///
/// # Arguments
///
/// * `json` - The JSON value to convert
///
/// # Returns
///
/// The corresponding `ScrapedData` representation
///
/// # Type Conversions
///
/// - `null` → `ScrapedData::None`
/// - `boolean` → `ScrapedData::String` (stringified)
/// - `integer` → `ScrapedData::Int` (as i32)
/// - `float` → `ScrapedData::Float` (as f32)
/// - `string` → `ScrapedData::String` (converted to markdown)
/// - `array` → `ScrapedData::Vec` (recursively converted)
/// - `object` → `ScrapedData::Map` (recursively converted and sorted by key)
///
/// # Examples
///
/// ```
/// use serde_json::json;
/// use scripts::scraper::convert_json_to_scraped_data;
///
/// let json = json!({"name": "test", "count": 42});
/// let scraped = convert_json_to_scraped_data(&json);
/// // Returns ScrapedData::Map with sorted keys
/// ```
#[must_use]
pub fn convert_json_to_scraped_data(json: &serde_json::Value) -> ScrapedData {
    match json {
        serde_json::Value::Null => ScrapedData::None,
        serde_json::Value::Bool(b) => ScrapedData::String(b.to_string()),
        serde_json::Value::Number(n) =>
            if n.is_i64() {
                ScrapedData::Int(n.as_i64().unwrap() as i32)
            } else if n.is_f64() {
                ScrapedData::Float(n.as_f64().unwrap() as f32)
            } else {
                ScrapedData::None
            },
        serde_json::Value::String(s) => ScrapedData::String(string_to_markdown(s)),
        serde_json::Value::Array(a) => {
            let mut results = Vec::new();
            for json in a {
                results.push(convert_json_to_scraped_data(json));
            }
            ScrapedData::Vec(results)
        },
        serde_json::Value::Object(o) => {
            let mut results = LinkedHashMap::new();
            for (k, v) in o {
                results.insert(k.clone(), convert_json_to_scraped_data(v));
            }
            ScrapedData::Map(sort_map_by_key(&mut results))
        },
    }
}

/// Converts HTML text to clean markdown format.
///
/// This function takes HTML content and converts it to markdown, then performs
/// additional cleanup operations:
/// - Normalizes whitespace (multiple spaces/nbsp become single spaces)
/// - Removes markdown links, keeping only the link text
/// - Removes carriage returns
/// - Filters out empty lines
/// - Joins lines with newlines
///
/// # Arguments
///
/// * `text` - The HTML text to convert to markdown
///
/// # Returns
///
/// Clean markdown text with normalized whitespace and simplified formatting
///
/// # Examples
///
/// ```
/// use scripts::scraper::string_to_markdown;
///
/// let html = "<p>Hello <strong>World</strong></p>";
/// let markdown = string_to_markdown(html);
/// // Returns cleaned markdown text
/// ```
#[must_use]
pub fn string_to_markdown(text: &str) -> String {
    htmd::convert(text).unwrap()
        .split('\n')
        // * Remove double spaces and markdown links keeping only the placeholder name
        .map(|s|
            REGEX_SPACES.replace_all(
                REGEX_MARKDOWN_LINK.replace_all(s, "$1").trim(),
                " ",
            ).replace('\r', ""),
        )
        .filter(|s| !s.is_empty())
        .collect::<Vec<_>>()
        .join("\n")
}

// Placeholder for extract_by_rules_static function
// This would need to be extracted from the binary for full testing
#[must_use]
pub const fn extract_by_rules_static(_element: &StaticElement, _rule: &Yaml, _debug: bool) -> ScrapedData {
    // This is a placeholder - the actual function is complex and would need to be
    // extracted from the binary for comprehensive testing
    ScrapedData::None
}
