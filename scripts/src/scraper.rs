//! Core scraper functionality extracted for testing

use std::{
    fmt::Display,
    ops::Deref,
    sync::LazyLock,
};

use hashlink::LinkedHashMap;
use htmd;
use regex::Regex;
use scraper::{
    ElementRef as StaticElement,
    Selector,
};
use serde_json;
use yaml_rust2::Yaml;

// * Models

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Keyword {
    Attribute,
    Dynamic,
    Follow,
    Key,
    Kind,
    Prefix,
    RemoveJsonKeys,
    Selector,
    Type,
    Url,
    Value,
    Variants,
}

/// Implement transparent use as &str
impl Deref for Keyword {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        match self {
            Self::Attribute => "attribute",
            Self::Dynamic => "dynamic",
            Self::Follow => "follow",
            Self::Key => "key",
            Self::Kind => "kind",
            Self::Prefix => "prefix",
            Self::RemoveJsonKeys => "remove_json_keys",
            Self::Selector => "selector",
            Self::Type => "type",
            Self::Url => "url",
            Self::Value => "value",
            Self::Variants => "variants",
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
pub enum ScrapedData {
    Vec(Vec<Self>),
    Map(LinkedHashMap<String, Self>),
    String(String),
    Float(f32),
    Int(i32),
    None,
}

impl Display for ScrapedData {
    fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Vec(vec) => {
                write!(fmt, "[")?;
                write!(
                    fmt,
                    "{}",
                    vec.iter()
                        .map(|item| format!("{item}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "]")
            },
            Self::Map(map) => {
                write!(fmt, "{{")?;
                write!(
                    fmt,
                    "{}",
                    map.iter()
                        .map(|(k, v)| format!("\"{k}\": {v}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "}}")
            },
            Self::String(s) => write!(
                fmt,
                "\"{}\"",
                s.replace('\\', "\\\\")
                    .replace('\"', "\\\"")
                    .replace('\r', "")
                    .replace('\n', "\\n")
            ),
            Self::Float(f) => write!(fmt, "{f:.1}"),
            Self::Int(i) => write!(fmt, "{i}"),
            Self::None => write!(fmt, "null"),
        }
    }
}

impl AsRef<Vec<Self>> for ScrapedData {
    fn as_ref(&self) -> &Vec<Self> {
        match self {
            Self::Vec(vec) => vec,
            _ => panic!("Cannot deref non-Vec"),
        }
    }
}

impl AsRef<LinkedHashMap<String, Self>> for ScrapedData {
    fn as_ref(&self) -> &LinkedHashMap<String, Self> {
        match self {
            Self::Map(map) => map,
            _ => panic!("Cannot deref non-Map"),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ValueType {
    DynamicMap,
    Float,
    Int,
    Json,
    List,
    Map,
    Single,
    Quantity,
    String,
    Text,
    Url,
    Variants,
}

impl ValueType {
    #[must_use]
    pub fn from_str(s: &str) -> Self {
        match s.trim().to_lowercase().as_str() {
            "dynamic_map" => Self::DynamicMap,
            "float" => Self::Float,
            "int" => Self::Int,
            "json" => Self::Json,
            "list" => Self::List,
            "quantity" => Self::Quantity,
            "text" => Self::Text,
            "url" => Self::Url,
            "variants" => Self::Variants,
            _ => Self::String,
        }
    }
}

// * Regex patterns
pub static REGEX_SPACES: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"(\s|&nbsp;)+").unwrap());
pub static REGEX_URL: LazyLock<Regex> = LazyLock::new(|| {
    Regex::new(r"^(http(s)?:\/\/)(www\\.)?[-a-zA-Z0-9@:%._\\+~#=\{\}]{2,256}\.[a-z]{2,6}\/\b([-a-zA-Z0-9@:%_\\+.~#?&\/\/=\{\}]*)$").unwrap()
});
pub static REGEX_PLACEHOLDER: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\{(.*?)\}").unwrap());
pub static REGEX_MARKDOWN_LINK: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\[([^\]]+)\]\(([^)]+)\)").unwrap());

// * Helper functions

pub fn retry<F, R, E>(max_retries: usize, mut trial: F) -> Result<R, E>
where F: FnMut() -> Result<R, E> {
    let mut retries = 0;
    loop {
        match trial() {
            Ok(result) => return Ok(result),
            Err(err) => {
                if retries >= max_retries {
                    return Err(err);
                }
                retries += 1;
            },
        }
    }
}

#[must_use]
pub fn rule_contains_key(rule: &Yaml, key: Keyword) -> bool { !rule[&*key].is_badvalue() }

#[must_use]
pub fn rule_has_value(rule: &Yaml) -> bool {
    let has_value_key = rule_contains_key(rule, Keyword::Value);
    let has_variants_key = rule_contains_key(rule, Keyword::Variants);
    has_value_key || has_variants_key
}

#[must_use]
pub fn get_element_text(element: &StaticElement, rule: &Yaml) -> String {
    if rule_contains_key(rule, Keyword::Attribute) {
        element
            .value()
            .attr(rule[&*Keyword::Attribute].as_str().unwrap())
            .unwrap_or_default()
            .trim()
            .to_string()
    } else {
        element.text().next().unwrap_or_default().trim().to_string()
    }
}

pub fn get_key_from_element(element: &StaticElement, rule: &Yaml) -> String {
    let selector = Selector::parse(rule[&*Keyword::Key].as_str().unwrap()).unwrap();
    let key_elements = element.select(&selector).collect::<Vec<_>>();
    if key_elements.is_empty() {
        return String::new();
    }

    let key = key_elements
        .first()
        .unwrap()
        .text()
        .next()
        .unwrap()
        .trim()
        .to_lowercase();

    REGEX_SPACES.replace_all(&key, "_").to_string()
}

pub fn remove_key_from_json(json: &mut serde_json::Value, key: &str) {
    if key.contains('.') {
        let (first, rest) = key.split_once('.').unwrap();
        if let Some(json) = json.get_mut(first) {
            remove_key_from_json(json, rest);
        }
        return;
    }

    if json.is_object() {
        json.as_object_mut().unwrap().remove(key);
    } else if json.is_array() {
        for json in json.as_array_mut().unwrap() {
            remove_key_from_json(json, key);
        }
    }
}

pub fn sort_map_by_key<T>(map: &mut LinkedHashMap<String, T>) -> LinkedHashMap<String, T> {
    let mut keys = map.keys().cloned().collect::<Vec<_>>();
    keys.sort();

    keys.into_iter()
        .map(|k| (k.clone(), map.remove(&k).unwrap()))
        .collect()
}

#[must_use]
pub fn convert_json_to_scraped_data(json: &serde_json::Value) -> ScrapedData {
    match json {
        serde_json::Value::Null => ScrapedData::None,
        serde_json::Value::Bool(b) => ScrapedData::String(b.to_string()),
        serde_json::Value::Number(n) =>
            if n.is_i64() {
                ScrapedData::Int(n.as_i64().unwrap() as i32)
            } else if n.is_f64() {
                ScrapedData::Float(n.as_f64().unwrap() as f32)
            } else {
                ScrapedData::None
            },
        serde_json::Value::String(s) => ScrapedData::String(string_to_markdown(s)),
        serde_json::Value::Array(a) => {
            let mut results = Vec::new();
            for json in a {
                results.push(convert_json_to_scraped_data(json));
            }
            ScrapedData::Vec(results)
        },
        serde_json::Value::Object(o) => {
            let mut results = LinkedHashMap::new();
            for (k, v) in o {
                results.insert(k.clone(), convert_json_to_scraped_data(v));
            }
            ScrapedData::Map(sort_map_by_key(&mut results))
        },
    }
}

#[must_use]
pub fn string_to_markdown(text: &str) -> String {
    htmd::convert(text).unwrap()
        .split('\n')
        // * Remove double spaces and markdown links keeping only the placeholder name
        .map(|s|
            REGEX_SPACES.replace_all(
                REGEX_MARKDOWN_LINK.replace_all(s, "$1").trim(),
                " ",
            ).replace('\r', ""),
        )
        .filter(|s| !s.is_empty())
        .collect::<Vec<_>>()
        .join("\n")
}

// Placeholder for extract_by_rules_static function
// This would need to be extracted from the binary for full testing
#[must_use] 
pub const fn extract_by_rules_static(_element: &StaticElement, _rule: &Yaml, _debug: bool) -> ScrapedData {
    // This is a placeholder - the actual function is complex and would need to be
    // extracted from the binary for comprehensive testing
    ScrapedData::None
}
