# yaml-language-server: $schema=scrapper-schema.json
- akc:
    title: AKC
    url: https://www.akc.org/dog-breeds/page/{page}/
    kind: static
    page: 1..25
    # page: 1..1
    value:
        selector: .breed-type-card
        type: list
        value:
            - name:
                selector: h3
            - details:
                selector: a
                attribute: href
                type: url
                follow: true
                kind: dynamic
                value:
                    - description:
                        selector: .breed-page__about__read-more__text__less
                        type: text # Use <p> if not empty, otherwise use text()
                    - basic_details:
                        selector: .breed-page__hero__overview__icon-block-wrap > .breed-page__hero__overview__icon-block
                        type: list
                        value:
                            key: h3
                            value:
                                selector: p
                                type: float | string
                    - advanced_details:
                        # selector: "#breed-page__traits__all > .breed-trait-group > .breed-trait-group__column"
                        selector: .breed-trait-group__trait-all
                        type: list
                        value:
                            key: h4.breed-trait-group__header
                            variants:
                                - 1:
                                    selector: .breed-trait-score__choice--selected > span
                                - 2:
                                    selector: .breed-trait-score__score-unit--filled
                                    type: quantity
                    - extra_details:
                        # selector: .breed-table__wrap
                        selector: div[data-js-component="breedPage"]
                        attribute: data-js-props
                        type: json
                        remove_json_keys: ["ads", "breed.history.slides", "settings.show_modal_for_breeds"]
                        # type: string
                    # - list_example:
                    #     selector: .breed-page__traits_list
                    #     type: list
                    #     value:
                    #         type: string
                    #         selector: .breed-page__traits_list__item > p
            - image:
                selector: img
                attribute: data-src
                type: url
- purina:
    title: Purina
    url: https://www.purina.co.uk/find-a-pet/dog-breeds?page=%2C{page}
    kind: static
    page: 0..19
    # page: 0..0
    value:
        selector: .result-animal-container
        type: list
        value:
            - name:
                selector: h4 > a
            - details:
                selector: a
                attribute: href
                type: url
                kind: static
                prefix: https://www.purina.co.uk
                follow: true
                value:
                    - description:
                        selector: .field--name-field-nppe-bs-description
                        type: text
                    - basic_details:
                        # selector: .field--name-field-key-facts
                        selector: .key-facts-item
                        type: list
                        value:
                            key: .key-facts-title
                            value:
                                selector: .key-facts-text
                    - advanced_details:
                        selector: .field--name-field-c-subitems > .field__item
                        type: list
                        value:
                            key: h2 > button
                            type: list
                            variants:
                                - 1:
                                    selector: tr
                                    key: td:first-child
                                    value:
                                        selector: td:last-child
                                - 2:
                                    selector: .field--name-field-c-text
                                    type: text
            - image:
                selector: img
                attribute: src
                type: url
                prefix: https://www.purina.co.uk
