//! Common test utilities and helper functions

use scraper::Html;
use yaml_rust2::{Yam<PERSON>, Yaml<PERSON>oader};

/// Helper function to create a YAML rule from a string
pub fn yaml_from_str(yaml_str: &str) -> Yaml {
    let docs = YamlLoader::load_from_str(yaml_str).unwrap();
    docs[0].clone()
}

/// Helper function to create HTML document from string
pub fn html_from_str(html_str: &str) -> Html {
    Html::parse_fragment(html_str)
}
