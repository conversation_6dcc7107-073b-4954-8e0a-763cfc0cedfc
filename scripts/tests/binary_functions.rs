//! Tests for functions that were moved from binary to library
//!
//! These tests verify the extraction functions that are now available
//! in the library and can be properly unit tested.

use hashlink::LinkedHashMap;
use scraper::{
    Html,
    Selector,
};
use scripts::scraper::{
    Keyword,
    ScrapedData,
    analyze_dynamic_map_rule,
    analyze_list_processing_strategy,
    analyze_nested_value_rule,
    extract_map_rule_keys,
    extract_placeholder_values,
    generate_url_combinations,
    get_json_from_element,
    process_url_from_element,
};
use serde_json::json;

mod common;
use common::{
    html_from_str,
    yaml_from_str,
};

#[test]
fn test_get_json_from_element_basic() {
    // Test basic JSON extraction from an element
    let html_content = r#"
    <script type="application/json">
    {
        "name": "Test Product",
        "price": 29.99,
        "available": true,
        "tags": ["electronics", "gadget"]
    }
    </script>
    "#;

    let document = Html::parse_document(html_content);
    let script_selector = Selector::parse("script").unwrap();
    let script_element = document.select(&script_selector).next().unwrap();

    let rule = yaml_from_str("type: 'json'");
    let result = get_json_from_element(&script_element, &rule);

    // Verify the structure
    if let ScrapedData::Map(map) = result {
        assert_eq!(map.get("name").unwrap(), &ScrapedData::String("Test Product".to_string()));
        assert_eq!(map.get("price").unwrap(), &ScrapedData::Float(29.99));
        assert_eq!(map.get("available").unwrap(), &ScrapedData::String("true".to_string()));

        if let ScrapedData::Vec(tags) = map.get("tags").unwrap() {
            assert_eq!(tags.len(), 2);
            assert_eq!(tags[0], ScrapedData::String("electronics".to_string()));
            assert_eq!(tags[1], ScrapedData::String("gadget".to_string()));
        } else {
            panic!("Expected tags to be a Vec");
        }
    } else {
        panic!("Expected result to be a Map");
    }
}

#[test]
fn test_get_json_from_element_with_remove() {
    // Test JSON extraction with key removal
    let html_content = r#"
    <div data-json='{"id": 123, "name": "Product", "internal_code": "ABC123", "price": 19.99}'>
    </div>
    "#;

    let document = Html::parse_document(html_content);
    let div_selector = Selector::parse("div").unwrap();
    let div_element = document.select(&div_selector).next().unwrap();

    let rule = yaml_from_str(
        r#"
attribute: "data-json"
type: "json"
remove: ["internal_code", "id"]
"#,
    );

    let result = get_json_from_element(&div_element, &rule);

    // Verify the structure and that keys were removed
    if let ScrapedData::Map(map) = result {
        assert_eq!(map.get("name").unwrap(), &ScrapedData::String("Product".to_string()));
        assert_eq!(map.get("price").unwrap(), &ScrapedData::Float(19.99));

        // These keys should be removed
        assert!(map.get("id").is_none());
        assert!(map.get("internal_code").is_none());

        // Should only have 2 keys left
        assert_eq!(map.len(), 2);
    } else {
        panic!("Expected result to be a Map");
    }
}

#[test]
fn test_get_json_from_element_nested_removal() {
    // Test JSON extraction with nested key removal
    let html_content = r#"
    <script>
    {
        "product": {
            "name": "Widget",
            "internal_id": 456,
            "specs": {
                "weight": "2kg",
                "internal_code": "W123"
            }
        },
        "metadata": {
            "created": "2023-01-01",
            "internal_notes": "confidential"
        }
    }
    </script>
    "#;

    let document = Html::parse_document(html_content);
    let script_selector = Selector::parse("script").unwrap();
    let script_element = document.select(&script_selector).next().unwrap();

    let rule = yaml_from_str(
        r#"
type: "json"
remove: ["product.internal_id", "product.specs.internal_code", "metadata.internal_notes"]
"#,
    );

    let result = get_json_from_element(&script_element, &rule);

    // Verify nested key removal
    if let ScrapedData::Map(map) = result {
        if let ScrapedData::Map(product) = map.get("product").unwrap() {
            assert_eq!(product.get("name").unwrap(), &ScrapedData::String("Widget".to_string()));
            assert!(product.get("internal_id").is_none()); // Should be removed

            if let ScrapedData::Map(specs) = product.get("specs").unwrap() {
                assert_eq!(specs.get("weight").unwrap(), &ScrapedData::String("2kg".to_string()));
                assert!(specs.get("internal_code").is_none()); // Should be removed
            }
        }

        if let ScrapedData::Map(metadata) = map.get("metadata").unwrap() {
            assert_eq!(metadata.get("created").unwrap(), &ScrapedData::String("2023-01-01".to_string()));
            assert!(metadata.get("internal_notes").is_none()); // Should be removed
        }
    } else {
        panic!("Expected result to be a Map");
    }
}

#[test]
fn test_get_json_from_element_invalid_json() {
    // Test handling of invalid JSON
    let html_content = r#"
    <div>This is not valid JSON content</div>
    "#;

    let document = Html::parse_document(html_content);
    let div_selector = Selector::parse("div").unwrap();
    let div_element = document.select(&div_selector).next().unwrap();

    let rule = yaml_from_str("type: 'json'");
    let result = get_json_from_element(&div_element, &rule);

    // Should return None for invalid JSON (unwrap_or_default returns null)
    assert_eq!(result, ScrapedData::None);
}

#[test]
fn test_get_json_from_element_empty() {
    // Test handling of empty content
    let html_content = r#"<div></div>"#;

    let document = Html::parse_document(html_content);
    let div_selector = Selector::parse("div").unwrap();
    let div_element = document.select(&div_selector).next().unwrap();

    let rule = yaml_from_str("type: 'json'");
    let result = get_json_from_element(&div_element, &rule);

    // Should return None for empty content
    assert_eq!(result, ScrapedData::None);
}

#[test]
fn test_get_json_from_element_complex_structure() {
    // Test complex JSON structure extraction
    let html_content = r#"
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": "Advanced Widget",
        "offers": {
            "@type": "Offer",
            "price": "99.99",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
        },
        "aggregateRating": {
            "@type": "AggregateRating",
            "ratingValue": "4.5",
            "reviewCount": "127"
        },
        "brand": {
            "@type": "Brand",
            "name": "TechCorp"
        }
    }
    </script>
    "#;

    let document = Html::parse_document(html_content);
    let script_selector = Selector::parse("script").unwrap();
    let script_element = document.select(&script_selector).next().unwrap();

    let rule = yaml_from_str("type: 'json'");
    let result = get_json_from_element(&script_element, &rule);

    // Verify complex nested structure
    if let ScrapedData::Map(map) = result {
        assert_eq!(map.get("@type").unwrap(), &ScrapedData::String("Product".to_string()));
        assert_eq!(map.get("name").unwrap(), &ScrapedData::String("Advanced Widget".to_string()));

        // Check nested offers
        if let ScrapedData::Map(offers) = map.get("offers").unwrap() {
            assert_eq!(offers.get("price").unwrap(), &ScrapedData::String("99.99".to_string()));
            assert_eq!(offers.get("priceCurrency").unwrap(), &ScrapedData::String("USD".to_string()));
        }

        // Check nested rating
        if let ScrapedData::Map(rating) = map.get("aggregateRating").unwrap() {
            assert_eq!(rating.get("ratingValue").unwrap(), &ScrapedData::String("4.5".to_string()));
            assert_eq!(rating.get("reviewCount").unwrap(), &ScrapedData::String("127".to_string()));
        }

        // Check nested brand
        if let ScrapedData::Map(brand) = map.get("brand").unwrap() {
            assert_eq!(brand.get("name").unwrap(), &ScrapedData::String("TechCorp".to_string()));
        }
    } else {
        panic!("Expected result to be a Map");
    }
}

#[test]
fn test_get_json_from_element_array_structure() {
    // Test JSON array extraction
    let html_content = r#"
    <script>
    [
        {"name": "Item 1", "value": 10},
        {"name": "Item 2", "value": 20},
        {"name": "Item 3", "value": 30}
    ]
    </script>
    "#;

    let document = Html::parse_document(html_content);
    let script_selector = Selector::parse("script").unwrap();
    let script_element = document.select(&script_selector).next().unwrap();

    let rule = yaml_from_str("type: 'json'");
    let result = get_json_from_element(&script_element, &rule);

    // Verify array structure
    if let ScrapedData::Vec(items) = result {
        assert_eq!(items.len(), 3);

        for (i, item) in items.iter().enumerate() {
            if let ScrapedData::Map(item_map) = item {
                let expected_name = format!("Item {}", i + 1);
                let expected_value = (i + 1) * 10;

                assert_eq!(item_map.get("name").unwrap(), &ScrapedData::String(expected_name));
                assert_eq!(item_map.get("value").unwrap(), &ScrapedData::Int(expected_value as i32));
            } else {
                panic!("Expected item to be a Map");
            }
        }
    } else {
        panic!("Expected result to be a Vec");
    }
}

// ============================================================================
// URL Processing Tests (Network-free)
// ============================================================================

#[test]
fn test_process_url_from_element_basic() {
    // Test basic URL extraction from href attribute
    let html_content = r#"<a href="/product/123">Product Link</a>"#;
    let document = Html::parse_document(html_content);
    let link_selector = Selector::parse("a").unwrap();
    let link_element = document.select(&link_selector).next().unwrap();

    let rule = yaml_from_str(
        r#"
attribute: "href"
prefix: "https://example.com"
"#,
    );

    let (url, should_follow, placeholders) = process_url_from_element(&link_element, &rule);

    assert_eq!(url, "https://example.com/product/123");
    assert!(!should_follow); // No follow flag or value specified
    assert!(placeholders.is_empty());
}

#[test]
fn test_process_url_from_element_with_follow() {
    // Test URL processing with follow flag
    let html_content = r#"<a href="/api/data">API Link</a>"#;
    let document = Html::parse_document(html_content);
    let link_selector = Selector::parse("a").unwrap();
    let link_element = document.select(&link_selector).next().unwrap();

    let rule = yaml_from_str(
        r#"
attribute: "href"
prefix: "https://api.example.com"
follow: true
value:
  selector: "span"
  type: "string"
"#,
    );

    let (url, should_follow, placeholders) = process_url_from_element(&link_element, &rule);

    assert_eq!(url, "https://api.example.com/api/data");
    assert!(should_follow);
    assert!(placeholders.is_empty()); // No placeholders in this URL
}

#[test]
fn test_process_url_from_element_with_url_key() {
    // Test URL processing when URL is specified directly in rule
    let html_content = r#"<div>Some content</div>"#;
    let document = Html::parse_document(html_content);
    let div_selector = Selector::parse("div").unwrap();
    let div_element = document.select(&div_selector).next().unwrap();

    let rule = yaml_from_str(
        r#"
url: "https://direct.example.com/endpoint"
value:
  type: "json"
"#,
    );

    let (url, should_follow, placeholders) = process_url_from_element(&div_element, &rule);

    assert_eq!(url, "https://direct.example.com/endpoint");
    assert!(should_follow); // URL key implies follow
    assert!(placeholders.is_empty());
}

#[test]
fn test_extract_placeholder_values_array() {
    // Test placeholder extraction with array values
    let rule = yaml_from_str(
        r#"
id: ["1", "2", "3"]
category: ["electronics", "books"]
"#,
    );

    let url = "https://api.com/items/{id}/category/{category}";
    let placeholders = extract_placeholder_values(url, &rule);

    assert_eq!(placeholders.len(), 2);
    assert_eq!(placeholders.get("id").unwrap(), &vec!["1", "2", "3"]);
    assert_eq!(placeholders.get("category").unwrap(), &vec!["electronics", "books"]);
}

#[test]
fn test_extract_placeholder_values_range() {
    // Test placeholder extraction with range values
    let rule = yaml_from_str(
        r#"
page: "1..5"
year: "2020..2023"
"#,
    );

    let url = "https://api.com/data/{page}/year/{year}";
    let placeholders = extract_placeholder_values(url, &rule);

    assert_eq!(placeholders.len(), 2);
    assert_eq!(placeholders.get("page").unwrap(), &vec!["1", "2", "3", "4", "5"]);
    assert_eq!(placeholders.get("year").unwrap(), &vec!["2020", "2021", "2022", "2023"]);
}

#[test]
fn test_extract_placeholder_values_single() {
    // Test placeholder extraction with single values
    let rule = yaml_from_str(
        r#"
id: "123"
format: "json"
"#,
    );

    let url = "https://api.com/item/{id}.{format}";
    let placeholders = extract_placeholder_values(url, &rule);

    assert_eq!(placeholders.len(), 2);
    assert_eq!(placeholders.get("id").unwrap(), &vec!["123"]);
    assert_eq!(placeholders.get("format").unwrap(), &vec!["json"]);
}

#[test]
fn test_extract_placeholder_values_missing() {
    // Test placeholder extraction with missing values
    let rule = yaml_from_str(
        r#"
id: "123"
# category is missing
"#,
    );

    let url = "https://api.com/item/{id}/category/{category}";
    let placeholders = extract_placeholder_values(url, &rule);

    assert_eq!(placeholders.len(), 2);
    assert_eq!(placeholders.get("id").unwrap(), &vec!["123"]);
    assert_eq!(placeholders.get("category").unwrap(), &Vec::<String>::new()); // Empty for missing
}

#[test]
fn test_generate_url_combinations_simple() {
    // Test URL combination generation with simple placeholders
    let mut placeholders = LinkedHashMap::new();
    placeholders.insert("id".to_string(), vec!["1".to_string(), "2".to_string()]);
    placeholders.insert("type".to_string(), vec!["A".to_string(), "B".to_string()]);

    let template = "https://api.com/items/{id}/type/{type}";
    let urls = generate_url_combinations(template, &placeholders);

    assert_eq!(urls.len(), 4);
    assert!(urls.contains(&"https://api.com/items/1/type/A".to_string()));
    assert!(urls.contains(&"https://api.com/items/1/type/B".to_string()));
    assert!(urls.contains(&"https://api.com/items/2/type/A".to_string()));
    assert!(urls.contains(&"https://api.com/items/2/type/B".to_string()));
}

#[test]
fn test_generate_url_combinations_no_placeholders() {
    // Test URL combination generation with no placeholders
    let placeholders = LinkedHashMap::new();
    let template = "https://api.com/static/endpoint";
    let urls = generate_url_combinations(template, &placeholders);

    assert_eq!(urls.len(), 1);
    assert_eq!(urls[0], "https://api.com/static/endpoint");
}

#[test]
fn test_generate_url_combinations_empty_values() {
    // Test URL combination generation with empty placeholder values
    let mut placeholders = LinkedHashMap::new();
    placeholders.insert("id".to_string(), vec![]); // Empty values
    placeholders.insert("type".to_string(), vec!["A".to_string()]);

    let template = "https://api.com/items/{id}/type/{type}";
    let urls = generate_url_combinations(template, &placeholders);

    assert_eq!(urls.len(), 0); // Should return empty if any placeholder has no values
}

// ============================================================================
// Extraction Logic Analysis Tests (Network-free)
// ============================================================================

#[test]
fn test_analyze_nested_value_rule_with_value() {
    // Test nested value rule analysis with value key
    let rule = yaml_from_str(
        r#"
value:
  selector: "span"
  type: "string"
"#,
    );

    let (has_value, has_variants, variant_count) = analyze_nested_value_rule(&rule);

    assert!(has_value);
    assert!(!has_variants);
    assert_eq!(variant_count, 0);
}

#[test]
fn test_analyze_nested_value_rule_with_variants() {
    // Test nested value rule analysis with variants
    let rule = yaml_from_str(
        r#"
variants:
  - in_stock:
      selector: ".in-stock"
      type: "string"
  - out_of_stock:
      selector: ".out-of-stock"
      type: "string"
  - discontinued:
      selector: ".discontinued"
      type: "string"
"#,
    );

    let (has_value, has_variants, variant_count) = analyze_nested_value_rule(&rule);

    assert!(!has_value);
    assert!(has_variants);
    assert_eq!(variant_count, 3);
}

#[test]
fn test_analyze_nested_value_rule_with_both() {
    // Test nested value rule analysis with both value and variants
    let rule = yaml_from_str(
        r#"
value:
  selector: ".primary"
  type: "string"
variants:
  - fallback1:
      selector: ".fallback1"
  - fallback2:
      selector: ".fallback2"
"#,
    );

    let (has_value, has_variants, variant_count) = analyze_nested_value_rule(&rule);

    assert!(has_value); // Should prioritize value over variants
    assert!(has_variants);
    assert_eq!(variant_count, 2);
}

#[test]
fn test_analyze_nested_value_rule_empty() {
    // Test nested value rule analysis with empty rule
    let rule = yaml_from_str(
        r#"
selector: "div"
type: "string"
"#,
    );

    let (has_value, has_variants, variant_count) = analyze_nested_value_rule(&rule);

    assert!(!has_value);
    assert!(!has_variants);
    assert_eq!(variant_count, 0);
}

#[test]
fn test_extract_map_rule_keys_simple() {
    // Test map rule key extraction with simple rules
    let rule = yaml_from_str(
        r#"
- title:
    selector: "h1"
    type: "string"
- price:
    selector: ".price"
    type: "float"
- description:
    selector: ".desc"
    type: "string"
"#,
    );

    let keys = extract_map_rule_keys(&rule);

    assert_eq!(keys.len(), 3);
    assert_eq!(keys[0], "title");
    assert_eq!(keys[1], "price");
    assert_eq!(keys[2], "description");
}

#[test]
fn test_extract_map_rule_keys_complex() {
    // Test map rule key extraction with complex nested rules
    let rule = yaml_from_str(
        r#"
- basic_info:
    - name:
        selector: ".name"
        type: "string"
    - id:
        selector: ".id"
        type: "string"
- pricing:
    selector: ".price-container"
    value:
      selector: ".price"
      type: "float"
- availability:
    variants:
      - in_stock:
          selector: ".in-stock"
      - out_of_stock:
          selector: ".out-of-stock"
"#,
    );

    let keys = extract_map_rule_keys(&rule);

    assert_eq!(keys.len(), 3);
    assert_eq!(keys[0], "basic_info");
    assert_eq!(keys[1], "pricing");
    assert_eq!(keys[2], "availability");
}

#[test]
fn test_extract_map_rule_keys_empty() {
    // Test map rule key extraction with empty/invalid rules
    let empty_rule = yaml_from_str("[]");
    let keys = extract_map_rule_keys(&empty_rule);
    assert_eq!(keys.len(), 0);

    let non_array_rule = yaml_from_str("selector: 'div'");
    let keys = extract_map_rule_keys(&non_array_rule);
    assert_eq!(keys.len(), 0);
}

#[test]
fn test_analyze_dynamic_map_rule_complete() {
    // Test dynamic map rule analysis with key and value
    let rule = yaml_from_str(
        r#"
key:
  selector: ".item-name"
  type: "string"
value:
  selector: ".item-content"
  type: "string"
"#,
    );

    let (has_key, has_value, key_selector) = analyze_dynamic_map_rule(&rule);

    assert!(has_key);
    assert!(has_value);
    assert_eq!(key_selector, ".item-name");
}

#[test]
fn test_analyze_dynamic_map_rule_key_only() {
    // Test dynamic map rule analysis with key only
    let rule = yaml_from_str(
        r#"
key:
  selector: ".category"
"#,
    );

    let (has_key, has_value, key_selector) = analyze_dynamic_map_rule(&rule);

    assert!(has_key);
    assert!(!has_value);
    assert_eq!(key_selector, ".category");
}

#[test]
fn test_analyze_dynamic_map_rule_value_only() {
    // Test dynamic map rule analysis with value only
    let rule = yaml_from_str(
        r#"
value:
  selector: ".content"
  type: "string"
"#,
    );

    let (has_key, has_value, key_selector) = analyze_dynamic_map_rule(&rule);

    assert!(!has_key);
    assert!(has_value);
    assert_eq!(key_selector, "");
}

#[test]
fn test_analyze_dynamic_map_rule_empty() {
    // Test dynamic map rule analysis with empty rule
    let rule = yaml_from_str(
        r#"
selector: "div"
type: "string"
"#,
    );

    let (has_key, has_value, key_selector) = analyze_dynamic_map_rule(&rule);

    assert!(!has_key);
    assert!(!has_value);
    assert_eq!(key_selector, "");
}

#[test]
fn test_analyze_list_processing_strategy_empty() {
    // Test list processing strategy with no elements
    let rule = yaml_from_str(
        r#"
selector: ".item"
type: "string"
"#,
    );

    let (should_return_single, should_merge_maps, strategy) = analyze_list_processing_strategy(&rule, 0);

    assert!(!should_return_single);
    assert!(!should_merge_maps);
    assert_eq!(strategy, "empty");
}

#[test]
fn test_analyze_list_processing_strategy_single() {
    // Test list processing strategy with single element
    let rule = yaml_from_str(
        r#"
value:
  selector: ".content"
  type: "string"
"#,
    );

    let (should_return_single, should_merge_maps, strategy) = analyze_list_processing_strategy(&rule, 1);

    assert!(should_return_single);
    assert!(!should_merge_maps);
    assert_eq!(strategy, "single");
}

#[test]
fn test_analyze_list_processing_strategy_multiple() {
    // Test list processing strategy with multiple elements
    let rule = yaml_from_str(
        r#"
- title:
    selector: "h2"
    type: "string"
- content:
    selector: ".content"
    type: "string"
"#,
    );

    let (should_return_single, should_merge_maps, strategy) = analyze_list_processing_strategy(&rule, 5);

    assert!(!should_return_single);
    assert!(!should_merge_maps); // Simplified logic for testing
    assert_eq!(strategy, "multiple");
}

// ============================================================================
// Integration Tests for Extraction Logic Components
// ============================================================================

#[test]
fn test_extraction_rule_analysis_integration() {
    // Test integration of different rule analysis functions

    // Test a complex rule that combines multiple extraction strategies
    let complex_rule = yaml_from_str(
        r#"
products:
  selector: ".product"
  type: "list"
  value:
    - basic_info:
        - title:
            selector: "h3"
            type: "string"
        - price:
            selector: ".price"
            type: "float"
    - availability:
        variants:
          - in_stock:
              selector: ".in-stock"
              type: "string"
          - out_of_stock:
              selector: ".out-of-stock"
              type: "string"
    - metadata:
        key:
          selector: ".meta-key"
        value:
          selector: ".meta-value"
          type: "string"
"#,
    );

    // Test the main products rule
    assert_eq!(complex_rule["products"]["type"].as_str().unwrap(), "list");
    assert_eq!(complex_rule["products"]["selector"].as_str().unwrap(), ".product");

    // Test the value array structure
    let value_rules = &complex_rule["products"]["value"];
    assert!(value_rules.is_array());

    let keys = extract_map_rule_keys(value_rules);
    assert_eq!(keys, vec!["basic_info", "availability", "metadata"]);

    // Test basic_info nested rules
    let basic_info_keys = extract_map_rule_keys(&value_rules[0]["basic_info"]);
    assert_eq!(basic_info_keys, vec!["title", "price"]);

    // Test availability variants
    let (has_value, has_variants, variant_count) =
        analyze_nested_value_rule(&value_rules[1]["availability"]);
    assert!(!has_value);
    assert!(has_variants);
    assert_eq!(variant_count, 2);

    // Test metadata dynamic mapping
    let (has_key, has_value, key_selector) = analyze_dynamic_map_rule(&value_rules[2]["metadata"]);
    assert!(has_key);
    assert!(has_value);
    assert_eq!(key_selector, ".meta-key");
}

#[test]
fn test_real_world_extraction_scenarios() {
    // Test real-world extraction scenarios that combine multiple strategies

    // E-commerce product listing scenario
    let ecommerce_rule = yaml_from_str(
        r#"
product_list:
  selector: ".product-item"
  type: "list"
  value:
    - basic:
        - name:
            selector: ".product-name"
            type: "string"
        - price:
            selector: ".price"
            type: "float"
        - rating:
            selector: ".rating"
            type: "float"
    - availability:
        variants:
          - available:
              selector: ".available"
              type: "string"
          - sold_out:
              selector: ".sold-out"
              type: "string"
          - pre_order:
              selector: ".pre-order"
              type: "string"
    - specifications:
        key:
          selector: ".spec-name"
        value:
          selector: ".spec-value"
          type: "string"
"#,
    );

    let product_rules = &ecommerce_rule["product_list"]["value"];

    // Verify structure analysis
    let keys = extract_map_rule_keys(product_rules);
    assert_eq!(keys.len(), 3);
    assert!(keys.contains(&"basic".to_string()));
    assert!(keys.contains(&"availability".to_string()));
    assert!(keys.contains(&"specifications".to_string()));

    // Test availability variants analysis
    let (_, has_variants, variant_count) = analyze_nested_value_rule(&product_rules[1]["availability"]);
    assert!(has_variants);
    assert_eq!(variant_count, 3);

    // Test specifications dynamic mapping
    let (has_key, has_value, _) = analyze_dynamic_map_rule(&product_rules[2]["specifications"]);
    assert!(has_key);
    assert!(has_value);

    // Test list processing strategy
    let (single, merge, strategy) = analyze_list_processing_strategy(&ecommerce_rule, 10);
    assert!(!single);
    assert!(!merge);
    assert_eq!(strategy, "multiple");
}
