//! Integration tests for the scraper functionality
//! 
//! Since this is a binary crate, we include the scraper code directly
//! and test its functionality through integration tests.

use std::{
    fmt::Display,
    ops::Deref,
    sync::LazyLock,
};

use hashlink::LinkedHashMap;
use regex::Regex;
use scraper::{
    ElementRef as StaticElement,
    Html,
    Selector,
};
use yaml_rust2::{Yaml, YamlLoader};

// Include the scraper types and functions
// We'll copy the essential parts from the binary for testing

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
enum Keyword {
    Attribute,
    Dynamic,
    Follow,
    Key,
    Kind,
    Prefix,
    RemoveJsonKeys,
    Selector,
    Type,
    Url,
    Value,
    Variants,
}

impl Deref for Keyword {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        match self {
            Self::Attribute => "attribute",
            Self::Dynamic => "dynamic",
            Self::Follow => "follow",
            Self::Key => "key",
            Self::Kind => "kind",
            Self::Prefix => "prefix",
            Self::RemoveJsonKeys => "remove_json_keys",
            Self::Selector => "selector",
            Self::Type => "type",
            Self::Url => "url",
            Self::Value => "value",
            Self::Variants => "variants",
        }
    }
}

#[derive(Debug, Clone, PartialEq)]
enum ScrapedData {
    Vec(Vec<Self>),
    Map(LinkedHashMap<String, Self>),
    String(String),
    Float(f32),
    Int(i32),
    None,
}

impl Display for ScrapedData {
    fn fmt(&self, fmt: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Vec(vec) => {
                write!(fmt, "[")?;
                write!(
                    fmt,
                    "{}",
                    vec.iter()
                        .map(|item| format!("{item}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "]")
            },
            Self::Map(map) => {
                write!(fmt, "{{")?;
                write!(
                    fmt,
                    "{}",
                    map.iter()
                        .map(|(k, v)| format!("\"{k}\": {v}"))
                        .collect::<Vec<_>>()
                        .join(", ")
                )?;
                write!(fmt, "}}")
            },
            Self::String(s) => write!(
                fmt,
                "\"{}\"",
                s.replace('\\', "\\\\")
                    .replace('\"', "\\\"")
                    .replace('\r', "")
                    .replace('\n', "\\n")
            ),
            Self::Float(f) => write!(fmt, "{f:.1}"),
            Self::Int(i) => write!(fmt, "{i}"),
            Self::None => write!(fmt, "null"),
        }
    }
}

impl AsRef<Vec<Self>> for ScrapedData {
    fn as_ref(&self) -> &Vec<Self> {
        match self {
            Self::Vec(vec) => vec,
            _ => panic!("Cannot deref non-Vec"),
        }
    }
}

impl AsRef<LinkedHashMap<String, Self>> for ScrapedData {
    fn as_ref(&self) -> &LinkedHashMap<String, Self> {
        match self {
            Self::Map(map) => map,
            _ => panic!("Cannot deref non-Map"),
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
enum ValueType {
    DynamicMap,
    Float,
    Int,
    Json,
    List,
    Map,
    Single,
    Quantity,
    String,
    Text,
    Url,
    Variants,
}

impl ValueType {
    fn from_str(s: &str) -> Self {
        match s.trim().to_lowercase().as_str() {
            "dynamic_map" => Self::DynamicMap,
            "float" => Self::Float,
            "int" => Self::Int,
            "json" => Self::Json,
            "list" => Self::List,
            "quantity" => Self::Quantity,
            "text" => Self::Text,
            "url" => Self::Url,
            "variants" => Self::Variants,
            _ => Self::String,
        }
    }
}

// Regex patterns
static REGEX_SPACES: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"(\s|&nbsp;)+").unwrap());
static REGEX_URL: LazyLock<Regex> = LazyLock::new(|| {
    Regex::new(r"^(http(s)?:\/\/)(www\\.)?[-a-zA-Z0-9@:%._\\+~#=\{\}]{2,256}\.[a-z]{2,6}\/\b([-a-zA-Z0-9@:%_\\+.~#?&\/\/=\{\}]*)$").unwrap()
});
static REGEX_PLACEHOLDER: LazyLock<Regex> = LazyLock::new(|| Regex::new(r"\{(.*?)\}").unwrap());
static REGEX_MARKDOWN_LINK: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"\[([^\]]+)\]\(([^)]+)\)").unwrap());

// Helper functions
fn retry<F, R, E>(max_retries: usize, mut trial: F) -> Result<R, E>
where F: FnMut() -> Result<R, E> {
    let mut retries = 0;
    loop {
        match trial() {
            Ok(result) => return Ok(result),
            Err(err) => {
                if retries >= max_retries {
                    return Err(err);
                }
                retries += 1;
            },
        }
    }
}

fn rule_contains_key(rule: &Yaml, key: Keyword) -> bool { 
    !rule[&*key].is_badvalue() 
}

fn rule_has_value(rule: &Yaml) -> bool {
    let has_value_key = rule_contains_key(rule, Keyword::Value);
    let has_variants_key = rule_contains_key(rule, Keyword::Variants);
    has_value_key || has_variants_key
}

fn get_element_text(element: &StaticElement, rule: &Yaml) -> String {
    if rule_contains_key(rule, Keyword::Attribute) {
        element
            .value()
            .attr(rule[&*Keyword::Attribute].as_str().unwrap())
            .unwrap_or_default()
            .trim()
            .to_string()
    } else {
        element.text().next().unwrap_or_default().trim().to_string()
    }
}

fn get_key_from_element(element: &StaticElement, rule: &Yaml) -> String {
    let selector = Selector::parse(rule[&*Keyword::Key].as_str().unwrap()).unwrap();
    let key_elements = element.select(&selector).collect::<Vec<_>>();
    if key_elements.is_empty() {
        return String::new();
    }

    let key = key_elements
        .first()
        .unwrap()
        .text()
        .next()
        .unwrap()
        .trim()
        .to_lowercase();

    REGEX_SPACES.replace_all(&key, "_").to_string()
}

fn remove_key_from_json(json: &mut serde_json::Value, key: &str) {
    if key.contains('.') {
        let (first, rest) = key.split_once('.').unwrap();
        if let Some(json) = json.get_mut(first) {
            remove_key_from_json(json, rest);
        }
        return;
    }

    if json.is_object() {
        json.as_object_mut().unwrap().remove(key);
    } else if json.is_array() {
        for json in json.as_array_mut().unwrap() {
            remove_key_from_json(json, key);
        }
    }
}

fn sort_map_by_key<T>(map: &mut LinkedHashMap<String, T>) -> LinkedHashMap<String, T> {
    let mut keys = map.keys().cloned().collect::<Vec<_>>();
    keys.sort();

    keys.into_iter()
        .map(|k| (k.clone(), map.remove(&k).unwrap()))
        .collect()
}

fn convert_json_to_scraped_data(json: &serde_json::Value) -> ScrapedData {
    match json {
        serde_json::Value::Null => ScrapedData::None,
        serde_json::Value::Bool(b) => ScrapedData::String(b.to_string()),
        serde_json::Value::Number(n) =>
            if n.is_i64() {
                ScrapedData::Int(n.as_i64().unwrap() as i32)
            } else if n.is_f64() {
                ScrapedData::Float(n.as_f64().unwrap() as f32)
            } else {
                ScrapedData::None
            },
        serde_json::Value::String(s) => ScrapedData::String(s.clone()),
        serde_json::Value::Array(a) => {
            let mut results = Vec::new();
            for json in a {
                results.push(convert_json_to_scraped_data(json));
            }
            ScrapedData::Vec(results)
        },
        serde_json::Value::Object(o) => {
            let mut results = LinkedHashMap::new();
            for (k, v) in o {
                results.insert(k.clone(), convert_json_to_scraped_data(v));
            }
            ScrapedData::Map(sort_map_by_key(&mut results))
        },
    }
}

// Test helper functions
fn yaml_from_str(yaml_str: &str) -> Yaml {
    let docs = YamlLoader::load_from_str(yaml_str).unwrap();
    docs[0].clone()
}

fn html_from_str(html_str: &str) -> Html {
    Html::parse_fragment(html_str)
}

// Now include the tests
mod tests {
    use super::*;

    #[test]
    fn test_scraped_data_display() {
        // Test String display
        let data = ScrapedData::String("test string".to_string());
        assert_eq!(data.to_string(), "\"test string\"");

        // Test String with escaping
        let data = ScrapedData::String("test \"quoted\" string\nwith newline".to_string());
        assert_eq!(data.to_string(), "\"test \\\"quoted\\\" string\\nwith newline\"");

        // Test Int display
        let data = ScrapedData::Int(42);
        assert_eq!(data.to_string(), "42");

        // Test Float display
        let data = ScrapedData::Float(3.14);
        assert_eq!(data.to_string(), "3.1");

        // Test None display
        let data = ScrapedData::None;
        assert_eq!(data.to_string(), "null");

        // Test Vec display
        let data = ScrapedData::Vec(vec![ScrapedData::String("item1".to_string()), ScrapedData::Int(42)]);
        assert_eq!(data.to_string(), "[\"item1\", 42]");

        // Test Map display
        let mut map = LinkedHashMap::new();
        map.insert("key1".to_string(), ScrapedData::String("value1".to_string()));
        map.insert("key2".to_string(), ScrapedData::Int(42));
        let data = ScrapedData::Map(map);
        assert_eq!(data.to_string(), "{\"key1\": \"value1\", \"key2\": 42}");
    }
}
