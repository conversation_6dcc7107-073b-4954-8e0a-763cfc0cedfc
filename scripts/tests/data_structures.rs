//! Tests for core data structures: ScrapedData, Keyword, ValueType

use std::collections::HashSet;
use hashlink::LinkedHashMap;
use scripts::scraper::{ScrapedData, Keyword, ValueType};

#[test]
fn test_scraped_data_display() {
    // Test String display
    let data = ScrapedData::String("test string".to_string());
    assert_eq!(data.to_string(), "\"test string\"");

    // Test String with escaping
    let data = ScrapedData::String("test \"quoted\" string\nwith newline".to_string());
    assert_eq!(data.to_string(), "\"test \\\"quoted\\\" string\\nwith newline\"");

    // Test Int display
    let data = ScrapedData::Int(42);
    assert_eq!(data.to_string(), "42");

    // Test Float display
    let data = ScrapedData::Float(3.14);
    assert_eq!(data.to_string(), "3.1");

    // Test None display
    let data = ScrapedData::None;
    assert_eq!(data.to_string(), "null");

    // Test Vec display
    let data = ScrapedData::Vec(vec![ScrapedData::String("item1".to_string()), ScrapedData::Int(42)]);
    assert_eq!(data.to_string(), "[\"item1\", 42]");

    // Test Map display
    let mut map = LinkedHashMap::new();
    map.insert("key1".to_string(), ScrapedData::String("value1".to_string()));
    map.insert("key2".to_string(), ScrapedData::Int(42));
    let data = ScrapedData::Map(map);
    assert_eq!(data.to_string(), "{\"key1\": \"value1\", \"key2\": 42}");
}

#[test]
fn test_scraped_data_equality() {
    // Test equality for different types
    assert_eq!(ScrapedData::String("test".to_string()), ScrapedData::String("test".to_string()));
    assert_ne!(
        ScrapedData::String("test".to_string()),
        ScrapedData::String("other".to_string())
    );

    assert_eq!(ScrapedData::Int(42), ScrapedData::Int(42));
    assert_ne!(ScrapedData::Int(42), ScrapedData::Int(43));

    assert_eq!(ScrapedData::Float(3.14), ScrapedData::Float(3.14));
    assert_ne!(ScrapedData::Float(3.14), ScrapedData::Float(2.71));

    assert_eq!(ScrapedData::None, ScrapedData::None);
    assert_ne!(ScrapedData::None, ScrapedData::String("test".to_string()));

    // Test Vec equality
    let vec1 = ScrapedData::Vec(vec![ScrapedData::String("a".to_string()), ScrapedData::Int(1)]);
    let vec2 = ScrapedData::Vec(vec![ScrapedData::String("a".to_string()), ScrapedData::Int(1)]);
    let vec3 = ScrapedData::Vec(vec![ScrapedData::String("b".to_string()), ScrapedData::Int(1)]);
    assert_eq!(vec1, vec2);
    assert_ne!(vec1, vec3);

    // Test Map equality
    let mut map1 = LinkedHashMap::new();
    map1.insert("key".to_string(), ScrapedData::String("value".to_string()));
    let mut map2 = LinkedHashMap::new();
    map2.insert("key".to_string(), ScrapedData::String("value".to_string()));
    let mut map3 = LinkedHashMap::new();
    map3.insert("key".to_string(), ScrapedData::String("other".to_string()));

    assert_eq!(ScrapedData::Map(map1), ScrapedData::Map(map2.clone()));
    assert_ne!(ScrapedData::Map(map2), ScrapedData::Map(map3));
}

#[test]
fn test_scraped_data_as_ref() {
    // Test Vec as_ref
    let data = ScrapedData::Vec(vec![ScrapedData::String("test".to_string())]);
    let vec_ref: &Vec<ScrapedData> = data.as_ref();
    assert_eq!(vec_ref.len(), 1);

    // Test Map as_ref
    let mut map = LinkedHashMap::new();
    map.insert("key".to_string(), ScrapedData::String("value".to_string()));
    let data = ScrapedData::Map(map);
    let map_ref: &LinkedHashMap<String, ScrapedData> = data.as_ref();
    assert_eq!(map_ref.len(), 1);
}

#[test]
#[should_panic(expected = "Cannot deref non-Vec")]
fn test_scraped_data_as_ref_vec_panic() {
    let data = ScrapedData::String("test".to_string());
    let _: &Vec<ScrapedData> = data.as_ref();
}

#[test]
#[should_panic(expected = "Cannot deref non-Map")]
fn test_scraped_data_as_ref_map_panic() {
    let data = ScrapedData::String("test".to_string());
    let _: &LinkedHashMap<String, ScrapedData> = data.as_ref();
}

#[test]
fn test_keyword_deref() {
    assert_eq!(&*Keyword::Attribute, "attribute");
    assert_eq!(&*Keyword::Dynamic, "dynamic");
    assert_eq!(&*Keyword::Follow, "follow");
    assert_eq!(&*Keyword::Key, "key");
    assert_eq!(&*Keyword::Kind, "kind");
    assert_eq!(&*Keyword::Prefix, "prefix");
    assert_eq!(&*Keyword::RemoveJsonKeys, "remove_json_keys");
    assert_eq!(&*Keyword::Selector, "selector");
    assert_eq!(&*Keyword::Type, "type");
    assert_eq!(&*Keyword::Url, "url");
    assert_eq!(&*Keyword::Value, "value");
    assert_eq!(&*Keyword::Variants, "variants");
}

#[test]
fn test_keyword_hash_and_eq() {
    // Test that keywords can be used in hash sets
    let mut set = HashSet::new();
    set.insert(Keyword::Selector);
    set.insert(Keyword::Type);
    set.insert(Keyword::Selector); // Duplicate should not increase size

    assert_eq!(set.len(), 2);
    assert!(set.contains(&Keyword::Selector));
    assert!(set.contains(&Keyword::Type));
    assert!(!set.contains(&Keyword::Attribute));
}

#[test]
fn test_value_type_from_str() {
    assert_eq!(ValueType::from_str("dynamic_map"), ValueType::DynamicMap);
    assert_eq!(ValueType::from_str("float"), ValueType::Float);
    assert_eq!(ValueType::from_str("int"), ValueType::Int);
    assert_eq!(ValueType::from_str("json"), ValueType::Json);
    assert_eq!(ValueType::from_str("list"), ValueType::List);
    assert_eq!(ValueType::from_str("quantity"), ValueType::Quantity);
    assert_eq!(ValueType::from_str("text"), ValueType::Text);
    assert_eq!(ValueType::from_str("url"), ValueType::Url);
    assert_eq!(ValueType::from_str("variants"), ValueType::Variants);
    assert_eq!(ValueType::from_str("unknown"), ValueType::String);
    assert_eq!(ValueType::from_str(""), ValueType::String);
}
