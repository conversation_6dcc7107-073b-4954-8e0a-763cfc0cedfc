//! End-to-end workflow integration tests
//! 
//! These tests verify complete scraping workflows from YAML rules to final output

use scripts::scraper::*;
use scraper::{Html, Selector};
use yaml_rust2::YamlLoader;

#[test]
fn test_complete_scraping_workflow() {
    // Test a complete scraping workflow: YAML -> HTML -> Data -> JSON
    
    let yaml_rules = r#"
title:
  selector: "h1"
  type: "string"
price:
  selector: ".price"
  type: "float"
features:
  selector: ".feature"
  type: "list"
  value:
    type: "string"
metadata:
  - id:
      attribute: "data-id"
      type: "string"
  - category:
      selector: ".category"
      type: "string"
"#;
    
    let html_content = r#"
    <html>
    <body data-id="product-123">
        <h1>Amazing Widget</h1>
        <div class="price">29.99</div>
        <div class="category">Electronics</div>
        <div class="features">
            <div class="feature">Waterproof</div>
            <div class="feature">Wireless</div>
            <div class="feature">Long Battery Life</div>
        </div>
    </body>
    </html>
    "#;
    
    // Parse YAML rules
    let docs = YamlLoader::load_from_str(yaml_rules).expect("Failed to parse YAML");
    let rules = &docs[0];
    
    // Parse HTML
    let document = Html::parse_document(html_content);
    let root = document.root_element();
    
    // Test individual rule processing
    assert!(!rules["title"].is_badvalue());
    assert!(!rules["price"].is_badvalue());
    assert!(!rules["features"].is_badvalue());
    assert!(rules["metadata"].is_array());
    
    // Test selector parsing
    let title_selector = Selector::parse("h1").unwrap();
    let title_element = document.select(&title_selector).next().unwrap();
    assert_eq!(title_element.text().collect::<String>(), "Amazing Widget");
    
    // Test price extraction and conversion
    let price_selector = Selector::parse(".price").unwrap();
    let price_element = document.select(&price_selector).next().unwrap();
    let price_text = price_element.text().collect::<String>();
    let price_value: f32 = price_text.parse().unwrap();
    assert_eq!(price_value, 29.99);
    
    // Test list extraction
    let feature_selector = Selector::parse(".feature").unwrap();
    let features: Vec<String> = document.select(&feature_selector)
        .map(|f| f.text().collect::<String>())
        .collect();
    assert_eq!(features, vec!["Waterproof", "Wireless", "Long Battery Life"]);
    
    // Test attribute extraction
    let body_selector = Selector::parse("body").unwrap();
    let body_element = document.select(&body_selector).next().unwrap();
    let id_attr = body_element.value().attr("data-id").unwrap();
    assert_eq!(id_attr, "product-123");
}

#[test]
fn test_complex_nested_extraction() {
    // Test extraction from complex nested HTML structures
    
    let html_content = r#"
    <html>
    <body>
        <div class="products">
            <article class="product" data-id="1">
                <h2>Product 1</h2>
                <div class="details">
                    <span class="price">$19.99</span>
                    <div class="rating">
                        <span class="stars">★★★★☆</span>
                        <span class="count">25 reviews</span>
                    </div>
                </div>
                <ul class="features">
                    <li>Feature A</li>
                    <li>Feature B</li>
                </ul>
            </article>
            <article class="product" data-id="2">
                <h2>Product 2</h2>
                <div class="details">
                    <span class="price">$39.99</span>
                    <div class="rating">
                        <span class="stars">★★★★★</span>
                        <span class="count">50 reviews</span>
                    </div>
                </div>
                <ul class="features">
                    <li>Feature X</li>
                    <li>Feature Y</li>
                    <li>Feature Z</li>
                </ul>
            </article>
        </div>
    </body>
    </html>
    "#;
    
    let document = Html::parse_document(html_content);
    
    // Test multiple product extraction
    let product_selector = Selector::parse(".product").unwrap();
    let products: Vec<_> = document.select(&product_selector).collect();
    assert_eq!(products.len(), 2);
    
    // Test nested data extraction for each product
    for (i, product) in products.iter().enumerate() {
        let expected_id = (i + 1).to_string();
        let actual_id = product.value().attr("data-id").unwrap();
        assert_eq!(actual_id, expected_id);
        
        // Test title extraction within product
        let title_selector = Selector::parse("h2").unwrap();
        let title = product.select(&title_selector).next().unwrap();
        let expected_title = format!("Product {}", i + 1);
        assert_eq!(title.text().collect::<String>(), expected_title);
        
        // Test price extraction within product
        let price_selector = Selector::parse(".price").unwrap();
        let price = product.select(&price_selector).next().unwrap();
        let price_text = price.text().collect::<String>();
        assert!(price_text.starts_with('$'));
        
        // Test features extraction within product
        let feature_selector = Selector::parse("li").unwrap();
        let features: Vec<String> = product.select(&feature_selector)
            .map(|li| li.text().collect::<String>())
            .collect();
        assert!(!features.is_empty());
        
        if i == 0 {
            assert_eq!(features, vec!["Feature A", "Feature B"]);
        } else {
            assert_eq!(features, vec!["Feature X", "Feature Y", "Feature Z"]);
        }
    }
}

#[test]
fn test_data_transformation_pipeline() {
    // Test the complete data transformation pipeline
    use hashlink::LinkedHashMap;
    
    // Simulate scraped data creation
    let mut product_data = LinkedHashMap::new();
    
    // Add different data types
    product_data.insert("name".to_string(), ScrapedData::String("Test Product".to_string()));
    product_data.insert("price".to_string(), ScrapedData::Float(29.99));
    product_data.insert("quantity".to_string(), ScrapedData::Int(100));
    product_data.insert("available".to_string(), ScrapedData::String("true".to_string()));
    
    // Add nested array
    let features = vec![
        ScrapedData::String("Durable".to_string()),
        ScrapedData::String("Lightweight".to_string()),
        ScrapedData::String("Eco-friendly".to_string()),
    ];
    product_data.insert("features".to_string(), ScrapedData::Vec(features));
    
    // Add nested object
    let mut specs = LinkedHashMap::new();
    specs.insert("weight".to_string(), ScrapedData::String("2.5kg".to_string()));
    specs.insert("dimensions".to_string(), ScrapedData::String("30x20x10cm".to_string()));
    product_data.insert("specifications".to_string(), ScrapedData::Map(specs));
    
    let scraped_data = ScrapedData::Map(product_data);
    
    // Test JSON-like output generation
    let json_output = scraped_data.to_string();
    
    // Verify structure
    assert!(json_output.contains("\"name\": \"Test Product\""));
    assert!(json_output.contains("\"price\": 30.0")); // Float formatting
    assert!(json_output.contains("\"quantity\": 100"));
    assert!(json_output.contains("\"features\": ["));
    assert!(json_output.contains("\"Durable\""));
    assert!(json_output.contains("\"specifications\": {"));
    assert!(json_output.contains("\"weight\": \"2.5kg\""));
    
    // Test that it's valid JSON-like structure
    assert!(json_output.starts_with('{'));
    assert!(json_output.ends_with('}'));
}

#[test]
fn test_error_recovery_and_fallbacks() {
    // Test how the system handles various error conditions
    
    let html_content = r#"
    <html>
    <body>
        <div class="valid">Valid content</div>
        <!-- Missing expected elements -->
        <div class="partial">
            <span class="title">Title only</span>
            <!-- Missing price element -->
        </div>
    </body>
    </html>
    "#;
    
    let document = Html::parse_document(html_content);
    
    // Test extraction from missing elements
    let missing_selector = Selector::parse(".nonexistent").unwrap();
    let missing_elements: Vec<_> = document.select(&missing_selector).collect();
    assert_eq!(missing_elements.len(), 0);
    
    // Test partial data extraction
    let partial_selector = Selector::parse(".partial").unwrap();
    let partial_element = document.select(&partial_selector).next().unwrap();
    
    let title_selector = Selector::parse(".title").unwrap();
    let title = partial_element.select(&title_selector).next().unwrap();
    assert_eq!(title.text().collect::<String>(), "Title only");
    
    let price_selector = Selector::parse(".price").unwrap();
    let price_elements: Vec<_> = partial_element.select(&price_selector).collect();
    assert_eq!(price_elements.len(), 0); // Should handle missing gracefully
    
    // Test invalid data type conversions
    let invalid_number = "not_a_number";
    assert!(invalid_number.parse::<i32>().is_err());
    assert!(invalid_number.parse::<f32>().is_err());
    
    // Test empty content handling
    let empty_html = "<html><body></body></html>";
    let empty_doc = Html::parse_document(empty_html);
    let body_selector = Selector::parse("body").unwrap();
    let body = empty_doc.select(&body_selector).next().unwrap();
    assert_eq!(body.text().collect::<String>(), "");
}

#[test]
fn test_performance_with_large_documents() {
    // Test performance characteristics with larger HTML documents
    use std::time::Instant;
    
    // Generate a large HTML document
    let mut html_parts = vec!["<html><body>".to_string()];
    
    for i in 0..1000 {
        html_parts.push(format!(
            r#"<div class="item" data-id="{}">
                <h3>Item {}</h3>
                <span class="value">{}</span>
                <ul>
                    <li>Property A</li>
                    <li>Property B</li>
                </ul>
            </div>"#,
            i, i, i * 10
        ));
    }
    
    html_parts.push("</body></html>".to_string());
    let large_html = html_parts.join("\n");
    
    // Measure parsing time
    let start = Instant::now();
    let document = Html::parse_document(&large_html);
    let parse_duration = start.elapsed();
    
    // Measure selection time
    let start = Instant::now();
    let item_selector = Selector::parse(".item").unwrap();
    let items: Vec<_> = document.select(&item_selector).collect();
    let select_duration = start.elapsed();
    
    // Verify results
    assert_eq!(items.len(), 1000);
    
    // Performance should be reasonable (these are loose bounds)
    assert!(parse_duration.as_millis() < 1000, "Parsing took too long: {parse_duration:?}");
    assert!(select_duration.as_millis() < 100, "Selection took too long: {select_duration:?}");
    
    // Test extraction from large result set
    let start = Instant::now();
    let values: Vec<String> = items.iter()
        .take(100) // Just test first 100 for performance
        .map(|item| {
            let value_selector = Selector::parse(".value").unwrap();
            item.select(&value_selector)
                .next()
                .unwrap()
                .text()
                .collect::<String>()
        })
        .collect();
    let extract_duration = start.elapsed();
    
    assert_eq!(values.len(), 100);
    assert_eq!(values[0], "0");
    assert_eq!(values[99], "990");
    assert!(extract_duration.as_millis() < 100, "Extraction took too long: {extract_duration:?}");
}
