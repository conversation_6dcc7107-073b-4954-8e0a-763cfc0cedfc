//! Tests for helper functions

use hashlink::LinkedHashMap;
use serde_json::json;
use scripts::scraper::{
    ScrapedData, Keyword, retry, rule_contains_key, rule_has_value, 
    get_element_text, get_key_from_element, remove_key_from_json, 
    sort_map_by_key, convert_json_to_scraped_data, string_to_markdown,
    REGEX_SPACES, REGEX_URL, REGEX_PLACEHOLDER, REGEX_MARKDOWN_LINK
};

mod common;
use common::{yaml_from_str, html_from_str};

#[test]
fn test_retry_function() {
    // Test successful operation
    let mut counter = 0;
    let result = retry(3, || {
        counter += 1;
        if counter < 3 { Err("error") } else { Ok("success") }
    });
    assert_eq!(result, Ok("success"));
    assert_eq!(counter, 3);

    // Test max retries exceeded
    let mut counter = 0;
    let result: Result<&str, &str> = retry(2, || {
        counter += 1;
        Err("persistent error")
    });
    assert_eq!(result, Err("persistent error"));
    assert_eq!(counter, 3); // Initial attempt + 2 retries
}

#[test]
fn test_rule_contains_key() {
    let yaml_str = r#"
selector: "div"
type: "string"
"#;
    let rule = yaml_from_str(yaml_str);
    
    assert!(rule_contains_key(&rule, Keyword::Selector));
    assert!(rule_contains_key(&rule, Keyword::Type));
    assert!(!rule_contains_key(&rule, Keyword::Attribute));
    assert!(!rule_contains_key(&rule, Keyword::Follow));
}

#[test]
fn test_rule_has_value() {
    let yaml_str = r#"
selector: "div"
value:
  selector: "span"
"#;
    let rule = yaml_from_str(yaml_str);
    assert!(rule_has_value(&rule));

    let yaml_str = r#"
selector: "div"
variants:
  - option1:
      selector: "span"
"#;
    let rule = yaml_from_str(yaml_str);
    assert!(rule_has_value(&rule));

    let yaml_str = r#"
selector: "div"
type: "string"
"#;
    let rule = yaml_from_str(yaml_str);
    assert!(!rule_has_value(&rule));
}

#[test]
fn test_get_element_text() {
    // Test text extraction
    let html = html_from_str("<div>Hello World</div>");
    let element = html.root_element();
    let rule = yaml_from_str("selector: 'div'");
    let text = get_element_text(&element, &rule);
    assert_eq!(text, "Hello World");

    // Test attribute extraction - need to select the actual img element
    let html = html_from_str("<img src='image.jpg' alt='test'>");
    let selector = scraper::Selector::parse("img").unwrap();
    let img_element = html.select(&selector).next().unwrap();
    let rule = yaml_from_str("attribute: 'src'");
    let text = get_element_text(&img_element, &rule);
    assert_eq!(text, "image.jpg");

    // Test missing attribute
    let html = html_from_str("<div>Hello</div>");
    let element = html.root_element();
    let rule = yaml_from_str("attribute: 'missing'");
    let text = get_element_text(&element, &rule);
    assert_eq!(text, "");
}

#[test]
fn test_get_key_from_element() {
    let html = html_from_str("<div><h3>Test Key</h3><p>content</p></div>");
    let element = html.root_element();
    let rule = yaml_from_str("key: 'h3'");
    
    let key = get_key_from_element(&element, &rule);
    assert_eq!(key, "test_key");
}

#[test]
fn test_remove_key_from_json() {
    // Test simple key removal
    let mut json = json!({"key1": "value1", "key2": "value2"});
    remove_key_from_json(&mut json, "key1");
    assert_eq!(json, json!({"key2": "value2"}));

    // Test nested key removal
    let mut json = json!({"outer": {"inner": "value", "keep": "this"}});
    remove_key_from_json(&mut json, "outer.inner");
    assert_eq!(json, json!({"outer": {"keep": "this"}}));

    // Test array handling
    let mut json = json!([{"key": "value1"}, {"key": "value2"}]);
    remove_key_from_json(&mut json, "key");
    assert_eq!(json, json!([{}, {}]));

    // Test that simple key removal only removes from top level objects (not nested objects)
    // but DOES remove from arrays recursively
    let mut json = json!({"keep": "this", "remove": "that", "nested": {"remove": "nested", "keep_nested": "value"}});
    remove_key_from_json(&mut json, "remove");
    // Only the top-level "remove" key should be removed, nested object "remove" stays
    assert_eq!(
        json,
        json!({"keep": "this", "nested": {"remove": "nested", "keep_nested": "value"}})
    );
}

#[test]
fn test_sort_map_by_key() {
    let mut map = LinkedHashMap::new();
    map.insert("zebra".to_string(), ScrapedData::String("z".to_string()));
    map.insert("alpha".to_string(), ScrapedData::String("a".to_string()));
    map.insert("beta".to_string(), ScrapedData::String("b".to_string()));

    let sorted = sort_map_by_key(&mut map);
    let keys: Vec<&String> = sorted.keys().collect();
    assert_eq!(keys, vec!["alpha", "beta", "zebra"]);
}

#[test]
fn test_convert_json_to_scraped_data() {
    // Test null
    let json = json!(null);
    assert_eq!(convert_json_to_scraped_data(&json), ScrapedData::None);

    // Test boolean
    let json = json!(true);
    assert_eq!(convert_json_to_scraped_data(&json), ScrapedData::String("true".to_string()));

    // Test integer
    let json = json!(42);
    assert_eq!(convert_json_to_scraped_data(&json), ScrapedData::Int(42));

    // Test float
    let json = json!(3.14);
    assert_eq!(convert_json_to_scraped_data(&json), ScrapedData::Float(3.14));

    // Test string
    let json = json!("hello");
    if let ScrapedData::String(s) = convert_json_to_scraped_data(&json) {
        assert_eq!(s, "hello");
    } else {
        panic!("Expected String result");
    }

    // Test array
    let json = json!([1, 2, 3]);
    if let ScrapedData::Vec(vec) = convert_json_to_scraped_data(&json) {
        assert_eq!(vec.len(), 3);
        assert_eq!(vec[0], ScrapedData::Int(1));
        assert_eq!(vec[1], ScrapedData::Int(2));
        assert_eq!(vec[2], ScrapedData::Int(3));
    } else {
        panic!("Expected Vec result");
    }

    // Test object
    let json = json!({"key": "value", "number": 42});
    if let ScrapedData::Map(map) = convert_json_to_scraped_data(&json) {
        assert_eq!(map.len(), 2);
        assert_eq!(map.get("key"), Some(&ScrapedData::String("value".to_string())));
        assert_eq!(map.get("number"), Some(&ScrapedData::Int(42)));
    } else {
        panic!("Expected Map result");
    }
}

#[test]
fn test_string_to_markdown() {
    let html = "<p>Hello <strong>World</strong></p>";
    let result = string_to_markdown(html);
    assert!(result.contains("Hello"));
    assert!(result.contains("World"));
    
    // Test with markdown links
    let html = "<p>Visit <a href='http://example.com'>Example</a></p>";
    let result = string_to_markdown(html);
    assert!(result.contains("Visit"));
    assert!(result.contains("Example"));
    assert!(!result.contains("http://example.com"));
}

#[test]
fn test_regex_patterns() {
    // Test REGEX_SPACES
    let text = "hello   world\u{00A0}test";
    let result = REGEX_SPACES.replace_all(text, " ");
    assert_eq!(result, "hello world test");

    // Test REGEX_URL (basic validation) - the regex is quite strict
    assert!(REGEX_URL.is_match("https://www.example.com/path"));
    assert!(REGEX_URL.is_match("http://www.example.com/path"));
    assert!(!REGEX_URL.is_match("not-a-url"));
    assert!(!REGEX_URL.is_match("http://example.com")); // This regex requires www or longer domain

    // Test REGEX_PLACEHOLDER
    let text = "Hello {name}, you have {count} messages";
    let captures: Vec<&str> = REGEX_PLACEHOLDER
        .captures_iter(text)
        .map(|cap| cap.get(1).unwrap().as_str())
        .collect();
    assert_eq!(captures, vec!["name", "count"]);

    // Test REGEX_MARKDOWN_LINK
    let text = "Visit [Example](http://example.com) for more info";
    let result = REGEX_MARKDOWN_LINK.replace_all(text, "$1");
    assert_eq!(result, "Visit Example for more info");
}
